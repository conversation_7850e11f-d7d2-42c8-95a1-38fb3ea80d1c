import React, { useState } from 'react';
import { Card, IconCard, Tooltip, Modal, Button, Typography } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { AgentStrategyListItem } from '../agent-strategy/types/agent-strategy.types';
import { useDeleteAdminAgentStrategy, useRestoreAdminAgentStrategy } from '../agent-strategy/hooks/useAgentStrategy';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

interface AdminAgentStrategyCardProps {
  strategy: AgentStrategyListItem;
  /** Danh sách tất cả strategies */
  allStrategies?: AgentStrategyListItem[];
  /** Callback khi click edit */
  onEditStrategy?: (strategyId: string) => void;
  /** Callback khi click memories */
  onMemoriesStrategy?: (strategyId: string) => void;
  /** Có phải trang trash không */
  isTrashPage?: boolean;
  /** Callback khi thành công */
  onSuccess?: () => void;
}

/**
 * Component hiển thị thông tin của một Agent Strategy
 */
const AdminAgentStrategyCard: React.FC<AdminAgentStrategyCardProps> = ({
  strategy,
  onEditStrategy,
  onMemoriesStrategy,
  isTrashPage = false,
  onSuccess
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { success, error } = useSmartNotification();

  // Hooks cho API calls
  const deleteStrategyMutation = useDeleteAdminAgentStrategy();
  const restoreStrategyMutation = useRestoreAdminAgentStrategy();

  const handleEditStrategy = async () => {
    if (onEditStrategy) {
      // Gọi API get detail để lấy dữ liệu mới nhất
      onEditStrategy(strategy.id);
    }
  };

  const handleMemoriesStrategy = () => {
    if (onMemoriesStrategy) {
      onMemoriesStrategy(strategy.id);
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteStrategyMutation.mutateAsync(strategy.id);
      setShowDeleteModal(false);

      success({
        title: t('admin:agent.strategy.card.deleteSuccess'),
        message: t('admin:agent.strategy.card.deleteSuccess'),
      });

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error('Error deleting strategy:', err);
      error({
        title: t('admin:agent.strategy.card.deleteError'),
        message: t('admin:agent.strategy.card.deleteError'),
      });
    }
  };

  const handleRestoreClick = async () => {
    try {
      await restoreStrategyMutation.mutateAsync(strategy.id);

      success({
        title: t('admin:agent.strategy.card.restoreSuccess', 'Khôi phục thành công'),
        message: t('admin:agent.strategy.card.restoreSuccess', 'Khôi phục chiến lược thành công'),
      });

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error('Error restoring strategy:', err);
      error({
        title: t('admin:agent.strategy.card.restoreError', 'Lỗi khôi phục'),
        message: t('admin:agent.strategy.card.restoreError', 'Không thể khôi phục chiến lược'),
      });
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  return (
    <>
      <Card
        className="h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
        variant="elevated"
      >
        <div className="p-2">
          <div className="flex flex-col space-y-4">
            {/* Hàng 1: Avatar và tên */}
            <div className="flex items-center gap-3 overflow-hidden">
              <div className="relative w-16 h-16 sm:w-20 sm:h-20 flex-shrink-0">
                <div className="w-full h-full relative">
                  <img
                    src="/assets/images/frame-level-agents.png"
                    alt="Strategy frame"
                    className="absolute inset-0 w-full h-full object-contain z-10"
                  />
                  <div className="absolute inset-0 flex items-center justify-center z-0">
                    <img
                      src={strategy.avatar || '/assets/images/default-avatar.png'}
                      alt={strategy.name}
                      className="w-12 h-12 sm:w-16 sm:h-16 rounded-full object-cover"
                    />
                  </div>
                </div>
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex flex-col">
                  <Typography
                    variant="h6"
                    className="font-semibold text-gray-900 dark:text-white truncate"
                  >
                    {strategy.name}
                  </Typography>
                  <Typography
                    variant="body2"
                    className="text-gray-500 dark:text-gray-400 truncate"
                  >
                    {strategy.modelId}
                  </Typography>
                </div>
              </div>
            </div>

            {/* Hàng 2: Thông tin bổ sung */}


            {/* Hàng 3: Các nút chức năng */}
            <div className="flex justify-end space-x-2">
              {isTrashPage ? (
                // Buttons cho trang trash
                <Tooltip content={t('admin:agent.strategy.card.restore', 'Khôi phục')} position="top">
                  <IconCard
                    icon="refresh-cw"
                    variant="default"
                    size="md"
                    onClick={handleRestoreClick}
                    className="text-green-500 hover:text-green-600"
                    disabled={restoreStrategyMutation.isPending}
                  />
                </Tooltip>
              ) : (
                // Buttons cho trang chính
                <>
                  <Tooltip content={t('admin:agent.strategy.card.memories', 'Bộ nhớ')} position="top">
                    <IconCard
                      icon="clock"
                      variant="default"
                      size="md"
                      onClick={handleMemoriesStrategy}
                      className="text-purple-500 hover:text-purple-600"
                    />
                  </Tooltip>

                  <Tooltip content={t('admin:agent.strategy.card.edit', 'Chỉnh sửa')} position="top">
                    <IconCard
                      icon="edit"
                      variant="default"
                      size="md"
                      onClick={handleEditStrategy}
                      className="text-blue-500 hover:text-blue-600"
                    />
                  </Tooltip>

                  <Tooltip content={t('admin:agent.strategy.card.delete', 'Xóa')} position="top">
                    <IconCard
                      icon="trash"
                      variant="default"
                      size="md"
                      onClick={handleDeleteClick}
                      className="text-red-500 hover:text-red-600"
                      disabled={deleteStrategyMutation.isPending}
                    />
                  </Tooltip>
                </>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        title={t('admin:agent.strategy.card.confirmDelete')}
        footer={


          <div className="flex justify-end space-x-3 pt-4 border-t border-border">
         
            <Button
              variant="primary"
              onClick={handleDeleteConfirm}
              isLoading={deleteStrategyMutation.isPending}
            >
              {t('admin:agent.strategy.card.delete', 'Xóa')}
            </Button>
          </div>

        }
      >

        <div className="space-y-4">
          <p className="text-gray-600 dark:text-gray-300">
            {t('admin:agent.strategy.card.deleteMessage')}
          </p>

          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={strategy.avatar || '/assets/images/default-avatar.png'}
                alt={strategy.name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{strategy.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {strategy.systemModelId ? `Model: ${strategy.systemModelId.slice(0, 8)}...` : t('common:unknown', 'Unknown')}
                </p>
              </div>
            </div>
          </div>
        </div>

      </Modal >
    </>
  );
};

export default AdminAgentStrategyCard;
