import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Icon,
  IconCard,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { NotificationUtil } from '@/shared/utils/notification';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { adminTypeAgentService } from '../agent-type/services/type-agent.service';

/**
 * Interface cho Model Registry
 */
export interface ModelRegistry {
  id: string;
  provider: string;
  modelBaseId: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho query parameters
 */
interface ModelRegistryQueryParams {
  page?: number;
  limit?: number;
  search?: string;
}

/**
 * API function để lấy danh sách Model Registries
 */
const getModelRegistries = async (
  params: ModelRegistryQueryParams = {}
): Promise<ApiResponseDto<PaginatedResult<ModelRegistry>>> => {
  const queryParams = new URLSearchParams();

  if (params.page) queryParams.append('page', params.page.toString());
  if (params.limit) queryParams.append('limit', params.limit.toString());
  if (params.search) queryParams.append('search', params.search);

  return apiClient.get(`/admin/model-registry?${queryParams.toString()}`);
};

/**
 * Props cho component ModelRegistrySlideInForm
 */
interface ModelRegistrySlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Mode: create hoặc edit
   */
  mode: 'create' | 'edit';

  /**
   * ID của agent type (cần thiết cho mode edit)
   */
  agentTypeId?: string | undefined;

  /**
   * Callback khi có Model Registry được thêm/cập nhật
   */
  onSave?: (modelRegistryIds: string[]) => void;

  /**
   * Danh sách Model Registry IDs đã chọn ban đầu
   */
  initialSelectedIds?: string[];
}

/**
 * Component form trượt để chọn các Model Registries
 */
const ModelRegistrySlideInForm: React.FC<ModelRegistrySlideInFormProps> = ({
  isVisible,
  onClose,
  mode,
  agentTypeId,
  onSave,
  initialSelectedIds = [],
}) => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  // Khởi tạo selectedIds từ initialSelectedIds
  useEffect(() => {
    if (initialSelectedIds.length > 0 && selectedIds.length === 0) {
      setSelectedIds(initialSelectedIds);
    }
  }, [initialSelectedIds, selectedIds.length]);

  // Load dữ liệu model registries đã có trong mode edit
  useEffect(() => {
    const loadExistingModels = async () => {
      if (mode === 'edit' && agentTypeId && isVisible) {
        try {
          const existingModelIds = await adminTypeAgentService.getModels(parseInt(agentTypeId));
          setSelectedIds(existingModelIds);
        } catch (error) {
          console.error('Error loading existing models:', error);
        }
      }
    };

    loadExistingModels();
  }, [mode, agentTypeId, isVisible]);

  // API hooks
  const { data: registriesResponse, isLoading } = useQuery({
    queryKey: ['admin-model-registries', page, limit, searchTerm],
    queryFn: () => getModelRegistries({
      page,
      limit,
      ...(searchTerm && { search: searchTerm })
    }),
    enabled: isVisible,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Lấy dữ liệu từ API response
  const registries: ModelRegistry[] = registriesResponse?.result?.items || [];

  // Filter registries based on search term
  const filteredRegistries = registries.filter(registry =>
    (registry.modelBaseId?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (registry.provider?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );

  // Cấu hình cột cho bảng
  const columns: TableColumn<ModelRegistry>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'model',
      title: t('admin:agent.form.modelRegistrySlideIn.model', 'Model'),
      dataIndex: 'name',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          {/* Icon */}
          <div className="w-8 h-8 rounded-full overflow-hidden mr-3 bg-gradient-to-br from-purple-500 to-pink-600 flex-shrink-0 flex items-center justify-center">
            <Icon name="cpu" size="sm" className="text-white" />
          </div>
          <div>
            <Typography variant="subtitle1">{record.id.slice(0, 8) + '...' || 'N/A'}</Typography>
            <Typography variant="caption" className="text-gray-500">
              Provider: {record.provider || 'Unknown'}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'modelNamePattern',
      title: t('admin:agent.form.modelRegistrySlideIn.modelNamePattern', 'Model Pattern'),
      dataIndex: 'modelNamePattern',
      width: '25%',
      render: (_, record) => (
        <Typography variant="body2" className="font-mono text-sm">
          {record.modelBaseId || 'N/A'}
        </Typography>
      ),
    },
    {
      key: 'provider',
      title: t('admin:agent.form.modelRegistrySlideIn.provider', 'Provider'),
      dataIndex: 'provider',
      width: '15%',
      render: (_, record) => (
        <span className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full">
          {record.provider || 'Unknown'}
        </span>
      ),
    },
  ];

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  // Xử lý lưu
  const handleSave = async () => {
    if (mode === 'edit') {
      if (!agentTypeId) {
        NotificationUtil.error({
          message: t('admin:agent.form.modelRegistrySlideIn.cannotSaveInThisMode', 'Cannot save in this mode'),
        });
        return;
      }

      setIsSubmitting(true);
      try {
        // Mode edit: Gọi API để cập nhật model registries
        await adminTypeAgentService.updateModels(parseInt(agentTypeId), selectedIds);

        // Sau khi cập nhật thành công, gọi callback để cập nhật UI
        if (onSave) {
          onSave(selectedIds);
        }

        NotificationUtil.success({
          message: t('admin:agent.form.modelRegistrySlideIn.updateModelsSuccess', 'Models updated successfully'),
        });

        onClose();
      } catch (error) {
        NotificationUtil.error({
          message: t('admin:agent.form.modelRegistrySlideIn.updateModelsError', 'Failed to update models'),
        });
        console.error('Error updating models:', error);
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Create mode: trả về danh sách model registry IDs đã chọn
      if (onSave) {
        onSave(selectedIds);
      }

      NotificationUtil.success({
        message: t('admin:agent.form.modelRegistrySlideIn.addModelsToListSuccess', 'Models added to list successfully'),
      });

      onClose();
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    setSearchTerm('');
    onClose();
  }, [onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'filter-all',
      label: t('admin:agent.form.modelRegistrySlideIn.filterBy', 'Filter by'),
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'all',
      label: t('admin:agent.form.modelRegistrySlideIn.all', 'All'),
      onClick: () => { },
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <div className="w-full">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">{t('admin:agent.form.modelRegistrySlideIn.title', 'Select Model Registries')}</Typography>
          <div className="flex justify-end space-x-2">
            <IconCard
              icon="save"
              variant="primary"
              size="md"
              title={t('admin:agent.form.modelRegistrySlideIn.save', 'Save')}
              onClick={handleSave}
              isLoading={isSubmitting}
              disabled={isLoading || isSubmitting || (mode === 'edit' && !agentTypeId) || selectedIds.length === 0}
            />
            <IconCard
              icon="x"
              variant="secondary"
              size="md"
              title={t('admin:agent.form.modelRegistrySlideIn.cancel', 'Cancel')}
              onClick={handleClose}
              disabled={isSubmitting}
            />

          </div>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <div className="overflow-hidden mb-4">
          <Table<ModelRegistry>
            columns={columns}
            data={filteredRegistries}
            rowKey="id"
            loading={isLoading}
            sortable={false}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
            pagination={{
              current: page,
              pageSize: limit,
              total: filteredRegistries.length,
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
              onChange: (newPage) => setPage(newPage),
            }}
          />
        </div>

        {/* Nút lưu */}

      </div>
    </SlideInForm>
  );
};

export default ModelRegistrySlideInForm;
