import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { Button, Icon, Typography, Modal } from '@/shared/components/common';
import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { useAdminAgentNotification } from '../hooks/useAdminAgentNotification';
import AdminMCPSlideInForm, { MCPSystem } from './AdminMCPSlideInForm';
import { AdminConfigWrapper } from './AdminConfigWrapper';

/**
 * Interface cho dữ liệu MCP Config
 */
export interface MCPConfigData {
  mcpSystemIds: string[];
}

interface AdminAgentSupervisorMCPConfigProps {
  initialData?: MCPConfigData;
  onSave?: (data: MCPConfigData) => void;
  mode?: 'create' | 'edit';
  agentSupervisorId?: string | undefined;
}

/**
 * API function để lấy thông tin chi tiết của MCP Systems
 */
const getMCPSystemsByIds = async (
  ids: string[]
): Promise<ApiResponseDto<MCPSystem[]>> => {
  if (ids.length === 0) {
    return { code: 200, message: 'Success', result: [] };
  }

  // Gọi API để lấy tất cả MCP systems và filter theo IDs
  const response = await apiClient.get<{ items: MCPSystem[] }>(`/admin/mcp-systems?limit=100`, {
    tokenType: 'admin',
  });
  const allMCPSystems = response.result.items || [];
  const filteredMCPSystems = allMCPSystems.filter(system => ids.includes(system.id));

  return {
    code: 200,
    message: 'Success',
    result: filteredMCPSystems,
  };
};

/**
 * API function để xóa MCP system khỏi agent supervisor (mode edit)
 */
const removeMCPSystemFromAgentSupervisor = async (
  agentSupervisorId: string,
  mcpSystemId: string
): Promise<void> => {
  await apiClient.delete(`/admin/agent-supervisor/${agentSupervisorId}/mcp-systems/${mcpSystemId}`, {
    tokenType: 'admin',
  });
};

const AdminAgentSupervisorMCPConfig: React.FC<AdminAgentSupervisorMCPConfigProps> = ({
  initialData,
  onSave,
  mode = 'create',
  agentSupervisorId
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const {
    deleteSuccess,
    deleteError
  } = useAdminAgentNotification();

  // State cho dữ liệu config
  const [configData, setConfigData] = useState<MCPConfigData>(
    initialData || {
      mcpSystemIds: [],
    }
  );

  // Load dữ liệu ban đầu trong mode edit - Agent supervisor đã có MCP data trong detail response
  useEffect(() => {
    if (initialData) {
      setConfigData(initialData);
    }
  }, [initialData]);

  // State cho form slide-in
  const [showMCPForm, setShowMCPForm] = useState(false);
  const [removingMCPId, setRemovingMCPId] = useState<string | null>(null);

  // State cho modal xác nhận xóa (chỉ dùng ở mode edit)
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [mcpToDelete, setMCPToDelete] = useState<MCPSystem | null>(null);

  // Query để lấy thông tin chi tiết của các MCP systems đã chọn
  const { data: selectedMCPSystemsResponse, refetch: refetchSelectedMCPSystems } = useQuery({
    queryKey: ['selected-mcp-systems', configData.mcpSystemIds],
    queryFn: () => getMCPSystemsByIds(configData.mcpSystemIds),
    enabled: configData.mcpSystemIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const selectedMCPSystems = selectedMCPSystemsResponse?.result || [];

  // Xử lý khi lưu từ AdminMCPSlideInForm
  const handleMCPFormSave = (selectedMCPIds: string[]) => {
    const newConfigData: MCPConfigData = {
      mcpSystemIds: selectedMCPIds,
    };

    setConfigData(newConfigData);

    // Gọi callback để cập nhật parent component
    if (onSave) {
      onSave(newConfigData);
    }

    // Refetch selected MCP systems để cập nhật UI
    if (selectedMCPIds.length > 0) {
      refetchSelectedMCPSystems();
    }
  };

  // Xử lý khi click vào icon xóa MCP system
  const handleRemoveClick = (mcpSystem: MCPSystem) => {
    if (mode === 'edit') {
      // Mode edit: Hiển thị modal xác nhận
      setMCPToDelete(mcpSystem);
      setShowDeleteModal(true);
    } else {
      // Mode create: Xóa trực tiếp
      handleRemoveMCPSystem(mcpSystem.id);
    }
  };

  // Xử lý xóa MCP system
  const handleRemoveMCPSystem = async (mcpSystemId: string) => {
    try {
      setRemovingMCPId(mcpSystemId);

      if (mode === 'edit' && agentSupervisorId) {
        // Mode edit: Gọi API để xóa khỏi database
        await removeMCPSystemFromAgentSupervisor(agentSupervisorId, mcpSystemId);
        deleteSuccess('MCP System');
      }

      // Cập nhật state local
      const newMCPIds = configData.mcpSystemIds.filter(id => id !== mcpSystemId);
      const newConfigData: MCPConfigData = {
        mcpSystemIds: newMCPIds,
      };

      setConfigData(newConfigData);

      // Gọi callback để cập nhật parent component
      if (onSave) {
        onSave(newConfigData);
      }

      // Refetch để cập nhật UI
      if (newMCPIds.length > 0) {
        refetchSelectedMCPSystems();
      }
    } catch (error) {
      console.error('Error removing MCP system:', error);
      deleteError('MCP System');
    } finally {
      setRemovingMCPId(null);
    }
  };

  // Xử lý xác nhận xóa từ modal
  const handleConfirmDelete = async () => {
    if (mcpToDelete) {
      await handleRemoveMCPSystem(mcpToDelete.id);
      setShowDeleteModal(false);
      setMCPToDelete(null);
    }
  };

  // Xử lý hủy xóa
  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setMCPToDelete(null);
  };

  // Xử lý hiển thị form thêm MCP
  const handleShowAddForm = () => {
    setShowMCPForm(true);
  };

  // Xử lý đóng form MCP
  const handleCloseMCPForm = () => {
    setShowMCPForm(false);
  };

  return (
    <AdminConfigWrapper
      title={
        <div className="flex items-center space-x-2">
          <Icon name="settings" size="sm" />
          <Typography variant="h6">
            {t('admin:agent.supervisor.mcpConfig.title', 'MCP Systems Configuration')}
          </Typography>
        </div>
      }
    >
      <div className="space-y-4">
        {/* Hiển thị danh sách MCP systems đã chọn */}
        {selectedMCPSystems.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {selectedMCPSystems.map((mcpSystem) => (
              <div
                key={mcpSystem.id}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800"
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <Icon name="server" size="sm" className="text-blue-500" />
                      <Typography variant="subtitle2" className="font-medium">
                        {mcpSystem.nameServer}
                      </Typography>
                    </div>
                    <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-2">
                      {mcpSystem.description}
                    </Typography>
                  
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveClick(mcpSystem)}
                    disabled={removingMCPId === mcpSystem.id}
                    isLoading={removingMCPId === mcpSystem.id}
                  >
                    <Icon name="trash-2" size="sm" className="text-red-500" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-3 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400">
            <Typography variant="body2" className='text-center'> 
              {t('admin:agent.supervisor.mcpConfig.noMCPSystems', 'No MCP systems configured')}
            </Typography>
          </div>
        )}

        {/* Nút thêm MCP system */}
        <div className='flex flex-col items-center pt-6 space-y-4'>
          <Button variant="outline" size="sm" onClick={handleShowAddForm}>
            <Icon name="plus" size="sm" className="mr-1" />
            {t('admin:agent.supervisor.mcpConfig.addMCPSystem', 'Add MCP System')}
          </Button>
        </div>
      </div>

      {/* MCP System Slide-in Form - Note: This uses agentTypeId prop, may need modification */}
      <AdminMCPSlideInForm
        isVisible={showMCPForm}
        onClose={handleCloseMCPForm}
        onSave={handleMCPFormSave}
        agentTypeId={agentSupervisorId} // This may need to be modified
        mode={mode}
        initialSelectedIds={configData.mcpSystemIds}
      />

      {/* Modal xác nhận xóa - chỉ hiển thị ở mode edit */}
      {mode === 'edit' && (
        <Modal
          isOpen={showDeleteModal}
          onClose={handleCancelDelete}
          title={t('admin:agent.common.confirmDelete', 'Xác nhận xóa')}
          size="md"
          footer={
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={handleCancelDelete}>
                {t('admin:agent.type.common.cancel', 'Hủy')}
              </Button>
              <Button
                variant="danger"
                onClick={handleConfirmDelete}
                isLoading={removingMCPId === mcpToDelete?.id}
                disabled={removingMCPId === mcpToDelete?.id}
              >
                {t('admin:agent.type.common.delete', 'Xóa')}
              </Button>
            </div>
          }
        >
          <Typography variant="body1">
            {t('admin:agent.supervisor.mcpConfig.confirmDeleteMessage', 
              'Bạn có chắc chắn muốn xóa MCP system "{name}" khỏi Agent Supervisor này?',
              { name: mcpToDelete?.nameServer }
            )}
          </Typography>
        </Modal>
      )}
    </AdminConfigWrapper>
  );
};

export default AdminAgentSupervisorMCPConfig;
