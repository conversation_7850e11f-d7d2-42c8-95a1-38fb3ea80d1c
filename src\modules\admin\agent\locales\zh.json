{"agent": {"management": {"title": "代理管理", "description": "综合代理系统管理"}, "notification": {"createSuccess": "{{entityName}}已成功创建", "updateSuccess": "{{entityName}}已成功更新", "deleteSuccess": "{{entityName}}已成功删除", "restoreSuccess": "{{entityName}}已成功恢复", "createError": "创建{{entityName}}时发生错误", "updateError": "更新{{entityName}}时发生错误", "deleteError": "删除{{entityName}}时发生错误", "restoreError": "恢复{{entityName}}时发生错误", "loadError": "无法加载{{entityName}}列表", "uploadSuccess": "上传成功", "uploadSuccessWithName": "{{fileName}}上传成功", "uploadError": "上传时发生错误", "validationError": "数据无效", "permissionError": "您没有权限执行此操作", "networkError": "网络连接错误。请重试", "processing": "正在{{action}}..."}, "rank": {"title": "代理等级管理", "description": "代理排名管理", "pageTitle": "代理等级管理", "addRank": "添加新等级", "editRank": "编辑代理等级", "searchPlaceholder": "搜索等级...", "noSearchResults": "未找到符合搜索条件的等级。", "createFirst": "创建第一个代理等级", "sortBy": "排序方式", "createSuccess": "成功", "createSuccessMessage": "代理等级已成功创建", "updateSuccess": "成功", "updateSuccessMessage": "代理等级已成功更新", "active": "活跃", "inactive": "非活跃", "editAction": "编辑", "deleteAction": "删除", "confirmDelete": "确认删除等级", "deleteMessage": "您确定要删除此等级吗？此操作无法撤销。", "deleteSuccess": "等级删除成功", "deleteError": "删除等级时发生错误", "memories": "记忆", "list": {"title": "等级列表", "noRanks": "没有等级", "noRanksDescription": "系统中目前没有等级。", "loadError": "无法加载等级列表。请重试。", "loading": "正在加载等级列表...", "refreshing": "正在刷新数据..."}, "form": {"basicInfo": "基本信息", "name": "等级名称", "namePlaceholder": "输入等级名称", "description": "描述", "descriptionPlaceholder": "输入等级描述", "expRange": "经验范围", "minExp": "最低经验", "maxExp": "最高经验", "badge": "徽章", "badgeUpload": "上传徽章", "badgeHelp": "支持格式：JPG、PNG（仅限单张图片）", "currentBadge": "当前徽章", "currentBadgeNote": "上传新图片以替换", "active": "激活", "create": "创建等级", "update": "更新", "creating": "正在创建等级...", "createSuccess": "等级创建成功", "createError": "创建等级时发生错误", "updateError": "更新等级时发生错误"}, "validation": {"nameRequired": "等级名称是必需的", "descriptionRequired": "描述是必需的", "minExpInvalid": "最低经验必须 >= 0", "minExpInteger": "最低经验必须是整数", "maxExpInvalid": "最高经验必须 > 0", "maxExpInteger": "最高经验必须是整数", "expRangeInvalid": "最高经验必须大于最低经验", "expRangeOverlap": "经验范围与其他等级的经验范围重叠"}, "edit": {"notFound": "未找到等级"}, "sort": {"name": "名称", "minExp": "最低经验", "maxExp": "最高经验", "createdAt": "创建日期"}}, "system": {"mcpSystem": {"title": "MCP系统", "pageTitle": "MCP系统管理", "pageDescription": "管理您的AI代理的模型上下文协议系统", "subtitle": "管理您的模型上下文协议系统", "loading": "正在加载MCP系统...", "noMCPSystems": "没有MCP系统", "noMCPSystemsDescription": "尚未创建任何MCP系统。创建您的第一个MCP系统以开始使用。", "addFirst": "添加第一个MCP系统", "addNew": "添加MCP系统", "edit": "编辑", "delete": "删除", "testConnection": "测试连接", "configInvalid": "配置无效，无法测试", "transport": "传输协议", "updated": "更新时间", "by": "更新者", "confirmDelete": "确认删除", "deleteWarning": "您确定要删除此MCP系统吗？此操作无法撤销。", "deleteSuccess": "MCP系统删除成功", "deleteError": "删除MCP系统失败", "testSuccess": "连接测试成功", "testError": "连接测试失败", "totalSystems": "系统总数", "httpSystems": "HTTP系统", "sseSystems": "SSE系统", "searchPlaceholder": "搜索MCP系统...", "manage": "管理", "systemMCPTitle": "系统MCP", "systemMCPSubtitle": "管理员MCP系统管理端点", "form": {"addDescription": "创建一个新的模型上下文协议系统", "basicInfo": "基本信息", "nameServer": "服务器名称", "nameServerPlaceholder": "输入MCP服务器名称", "description": "描述", "descriptionPlaceholder": "输入MCP系统描述", "config": "配置", "transport": "传输协议", "selectTransport": "选择传输协议", "httpDescription": "标准HTTP协议", "sseDescription": "服务器发送事件", "url": "URL", "urlPlaceholder": "输入MCP服务器URL", "headers": "请求头", "addHeader": "添加请求头", "headerName": "名称", "headerValue": "值", "headerNamePlaceholder": "请求头名称", "headerValuePlaceholder": "请求头值", "removeHeader": "删除请求头", "create": "创建MCP系统", "update": "更新MCP系统", "editDescription": "更新模型上下文协议系统配置", "loading": "正在加载MCP系统详情...", "validation": {"nameServerRequired": "服务器名称是必需的", "nameServerMinLength": "服务器名称至少需要2个字符", "descriptionRequired": "描述是必需的", "transportRequired": "传输协议是必需的", "urlRequired": "URL是必需的", "urlInvalid": "URL格式无效", "headerNameRequired": "请求头名称是必需的", "headerValueRequired": "请求头值是必需的"}, "createSuccess": "MCP系统创建成功", "createError": "创建MCP系统失败", "updateSuccess": "MCP系统更新成功", "updateError": "更新MCP系统失败", "loadError": "加载MCP系统详情失败"}}, "title": "系统代理管理", "noAgents": "暂无代理", "description": "系统代理管理", "pageTitle": "系统代理管理", "addAgent": "添加新代理", "editAgent": "编辑代理系统", "searchPlaceholder": "搜索代理...", "noSearchResults": "未找到符合搜索条件的代理。", "createFirst": "创建第一个系统代理", "viewTrash": "查看回收站", "backToMain": "返回主列表", "createSuccess": "成功", "createSuccessMessage": "代理系统已成功创建", "updateSuccess": "成功", "cancel": "取消", "updateAgent": "更新代理", "updateSuccessMessage": "代理系统已成功更新", "mcpConfig": {"title": "MCP系统", "loadingMCPSystems": "正在加载MCP系统...", "noMCPSystemsSelected": "未选择MCP系统", "addMCPSystem": "添加MCP系统", "removeMCPSystem": "移除MCP系统", "confirmDeleteMCPSystem": "您确定要从代理类型中移除MCP系统\"{{mcpSystemName}}\"吗？", "deleteMCPSystemWarning": "此操作无法撤销。"}, "mcpSlideIn": {"title": "选择MCP系统", "mcpSystem": "MCP系统", "description": "描述", "updatedAt": "更新时间", "save": "保存", "cancel": "取消", "filterBy": "筛选", "all": "全部", "updateMCPsSuccess": "MCP系统更新成功", "updateMCPsError": "更新MCP系统失败", "addMCPsToListSuccess": "MCP系统已成功添加到列表", "cannotSaveInThisMode": "在此模式下无法保存"}}, "user": {"title": "管理员 - 用户代理", "description": "用户代理管理"}, "type": {"title": "代理类型管理", "description": "代理类型管理", "pageTitle": "代理类型管理", "addType": "添加新代理类型", "editType": "编辑代理类型", "searchPlaceholder": "搜索代理类型...", "createFirst": "创建第一个代理类型", "createSuccess": "成功", "createSuccessMessage": "代理类型已成功创建", "updateSuccess": "成功", "updateSuccessMessage": "代理类型已成功更新", "cancel": "取消", "updateType": "更新代理类型", "viewTrash": "查看回收站", "backToMain": "返回主列表", "noSearchResults": "未找到符合搜索条件的代理类型。", "deleteConfirmTitle": "确认删除代理类型", "deleteConfirmMessage": "您确定要删除此代理类型吗？此操作将把代理类型移至回收站，可以恢复。", "deleteSuccess": "删除成功", "deleteSuccessMessage": "代理类型已成功删除", "restoreSuccess": "恢复成功", "restoreSuccessMessage": "代理类型已成功恢复", "selectTypeToDelete": "选择要删除的代理类型", "selectTypeToDeleteDescription": "选择您要删除的代理类型。代理类型将被移至回收站，可以恢复。", "deleteWithMigration": "删除并迁移", "deleteWithMigrationDescription": "删除代理类型并将此类型的所有代理迁移到新选择的类型。", "newTypeAgent": "新代理类型", "selectNewType": "选择新代理类型", "selectNewTypeDescription": "选择要将当前代理迁移到的新代理类型。", "noAvailableTypes": "没有其他代理类型可用于迁移。您只能删除而不迁移。", "deleteOnly": "仅删除", "deleteError": "删除代理类型时发生错误", "restoreError": "恢复代理类型时发生错误"}, "strategy": {"title": "代理策略管理", "description": "代理策略管理", "pageTitle": "代理策略管理", "addStrategy": "添加新策略", "editStrategy": "编辑代理策略", "searchPlaceholder": "搜索策略...", "noSearchResults": "未找到符合搜索条件的策略。", "createFirst": "创建第一个代理策略", "createSuccess": "成功", "createSuccessMessage": "代理策略已成功创建", "updateSuccess": "成功", "updateSuccessMessage": "代理策略已成功更新", "cancel": "取消", "updateStrategy": "更新策略", "viewTrash": "查看回收站", "backToMain": "返回主列表", "list": {"title": "策略列表", "noStrategies": "没有策略", "noStrategiesDescription": "系统中目前没有策略。", "loadError": "无法加载策略列表。请重试。", "loading": "正在加载策略列表...", "refreshing": "正在刷新数据..."}, "card": {"edit": "编辑", "delete": "删除", "restore": "恢复", "memories": "记忆", "confirmDelete": "确认删除策略", "deleteMessage": "您确定要删除此策略吗？此操作无法撤销。", "deleteSuccess": "策略删除成功", "deleteError": "删除策略时发生错误", "updateSuccess": "策略更新成功", "updateError": "更新策略时发生错误", "restoreSuccess": "策略恢复成功", "restoreError": "恢复策略时发生错误"}, "form": {"basicInfo": "基本信息", "name": "策略名称", "namePlaceholder": "输入策略名称", "avatar": "头像", "avatarUpload": "上传头像", "avatarHelp": "支持格式：JPG、PNG（仅限单张图片）", "modelConfig": "模型配置", "temperature": "温度", "maxTokens": "最大令牌数", "instruction": "指令", "instructionPlaceholder": "输入策略指令", "content": "步骤内容", "contentStep": "步骤 {step}", "contentPlaceholder": "输入步骤 {step} 的内容", "addStep": "添加步骤", "removeStep": "删除步骤", "exampleDefault": "默认示例", "exampleStep": "示例 {step}", "examplePlaceholder": "输入步骤 {step} 的示例", "addExample": "添加示例", "removeExample": "删除示例", "systemModel": "系统模型", "provider": "提供商类型", "selectProvider": "选择提供商", "selectProviderFirst": "请先选择提供商", "model": "模型", "selectSystemModel": "选择系统模型", "selectModel": "选择模型", "create": "创建策略", "update": "更新策略", "creating": "正在创建策略...", "updating": "正在更新策略...", "createSuccess": "策略创建成功", "createError": "创建策略时发生错误", "updateSuccess": "策略更新成功", "updateError": "更新策略时发生错误", "uploadingAvatar": "正在上传头像...", "uploadAvatarSuccess": "头像上传成功", "uploadAvatarError": "上传头像时发生错误", "loadSystemModelsError": "无法加载系统模型列表", "currentAvatar": "当前头像"}, "trash": {"title": "回收站 - 代理策略", "noStrategies": "回收站中没有策略", "noStrategiesDescription": "回收站为空。已删除的策略将出现在这里。", "noSearchResults": "未找到符合搜索条件的策略", "loadError": "无法加载已删除策略列表"}, "validation": {"nameRequired": "策略名称是必需的", "instructionRequired": "指令是必需的", "systemModelRequired": "系统模型是必需的", "providerRequired": "请选择提供商", "contentRequired": "步骤内容是必需的", "contentStepRequired": "步骤 {step} 的内容是必需的", "exampleRequired": "默认示例是必需的", "exampleStepRequired": "步骤 {step} 的示例是必需的"}}, "template": {"title": "代理模板", "description": "管理可用的代理模板", "pageTitle": "代理模板管理", "noTemplates": "暂无模板", "noTemplatesDescription": "系统中目前没有模板。", "noSearchResults": "未找到匹配的模板", "loadError": "无法加载模板列表。请重试。", "loading": "正在加载模板列表...", "refreshing": "正在刷新数据...", "viewTrash": "查看回收站", "backToMain": "返回主列表", "selectType": "选择代理类型", "selectTypeDescription": "选择适合您需求的代理类型。每种代理类型都有不同的能力和特征。", "createTitle": "创建代理", "editTitle": "编辑代理模板", "create": {"title": "创建代理模板"}, "profile": {"title": "个人资料信息", "dateOfBirth": "出生日期", "dateOfBirthPlaceholder": "选择出生日期", "gender": {"label": "性别", "placeholder": "选择性别", "male": "男", "female": "女", "other": "其他"}, "education": {"label": "教育程度", "placeholder": "选择教育程度", "highSchool": "高中", "college": "大专", "university": "大学", "master": "硕士", "phd": "博士"}, "languages": "语言", "languagesPlaceholder": "选择语言", "nations": "国家", "nationsPlaceholder": "选择国家", "position": "职位", "positionPlaceholder": "输入职位", "skills": "技能", "skillsPlaceholder": "输入技能并按回车", "personality": "性格", "personalityPlaceholder": "输入性格特征并按回车"}, "memories": {"title": "记忆配置", "description": "配置代理将使用的记忆，以提供更好的响应并维持上下文", "noMemories": "尚未配置记忆", "addMemory": "添加记忆", "editMemory": "编辑记忆", "memoryTitle": "标题", "titlePlaceholder": "输入记忆标题", "reason": "原因", "reasonPlaceholder": "输入此记忆的原因", "content": "内容", "contentPlaceholder": "输入记忆内容", "save": "保存记忆", "cancel": "取消", "edit": "编辑", "delete": "删除", "confirmDelete": "您确定要删除此记忆吗？", "deleteSuccess": "记忆删除成功", "deleteError": "删除记忆时出错", "saveSuccess": "记忆保存成功", "saveError": "保存记忆时出错", "validation": {"titleRequired": "记忆标题是必需的", "reasonRequired": "原因是必需的", "contentRequired": "记忆内容是必需的"}}, "conversion": {"title": "转换配置", "description": "配置在对话过程中将从用户收集的字段", "noFields": "尚未配置转换字段", "addField": "添加转换字段", "editField": "编辑转换字段", "fieldName": "字段名称", "fieldNamePlaceholder": "输入字段名称", "fieldDescription": "描述", "descriptionPlaceholder": "输入字段描述", "fieldType": "字段类型", "dataType": "数据类型", "required": "必需", "active": "激活", "save": "保存字段", "cancel": "取消", "edit": "编辑", "delete": "删除", "confirmDelete": "您确定要删除此字段吗？", "deleteSuccess": "字段删除成功", "deleteError": "删除字段时出错", "saveSuccess": "字段保存成功", "saveError": "保存字段时出错", "type": {"string": "字符串", "number": "数字", "boolean": "布尔值", "arrayString": "字符串数组", "arrayNumber": "数字数组", "enum": "枚举"}, "validation": {"nameRequired": "字段名称是必需的", "descriptionRequired": "描述是必需的", "typeRequired": "字段类型是必需的"}}, "name": "代理名称", "namePlaceholder": "输入代理名称", "provider": "提供商", "model": "模型", "selectModel": "选择模型", "strategy": "策略", "selectStrategy": "选择策略", "modelConfig": "模型配置", "temperature": "温度", "topP": "Top P", "topK": "Top K", "maxTokens": "最大令牌数", "instruction": "指令", "instructionPlaceholder": "输入模型指令...", "createSuccess": "代理模板创建成功", "createError": "创建代理模板失败", "updateSuccess": "代理模板更新成功", "updateError": "更新代理模板失败", "uploadingAvatar": "正在上传头像...", "uploadAvatarSuccess": "头像上传成功", "uploadAvatarError": "上传头像时发生错误", "isForSale": "销售支持", "isForSaleDescription": {"enabled": "此代理支持销售活动，可用于商业目的", "disabled": "此代理不支持销售，仅用于支持和咨询目的"}, "validation": {"nameRequired": "代理名称是必需的", "modelRequired": "模型选择是必需的", "strategyRequired": "策略选择是必需的"}, "card": {"model": "模型", "forSale": "销售支持", "supported": "支持", "notSupported": "不支持", "delete": "删除", "restore": "恢复", "memories": "记忆", "confirmDelete": "确认删除模板", "deleteMessage": "您确定要删除此模板吗？此操作无法撤销。", "deleteSuccess": "模板删除成功", "deleteError": "删除模板时发生错误", "restoreSuccess": "模板恢复成功", "restoreError": "恢复模板时发生错误"}, "trash": {"title": "回收站 - 代理模板", "noTemplates": "回收站中没有模板", "noTemplatesDescription": "回收站为空。已删除的模板将出现在这里。", "noSearchResults": "未找到匹配的模板", "loadError": "无法加载已删除的模板列表"}}, "supervisor": {"title": "代理监督员管理", "description": "管理代理监督员", "pageTitle": "代理监督员管理", "addSupervisor": "添加新代理监督员", "editSupervisor": "编辑代理监督员", "searchPlaceholder": "搜索代理监督员...", "noSearchResults": "未找到符合搜索条件的代理监督员。", "createFirst": "创建第一个代理监督员", "viewTrash": "查看回收站", "backToMain": "返回主列表", "createSuccess": "代理监督员创建成功", "updateSuccess": "代理监督员更新成功", "deleteSuccess": "代理监督员删除成功", "restoreSuccess": "代理监督员恢复成功", "createError": "创建代理监督员时发生错误", "updateError": "更新代理监督员时发生错误", "deleteError": "删除代理监督员时发生错误", "restoreError": "恢复代理监督员时发生错误", "toggleActiveSuccess": "状态更改成功", "toggleActiveError": "更改状态时发生错误", "list": {"title": "代理监督员列表", "noSupervisors": "没有代理监督员", "noSupervisorsDescription": "系统中目前没有代理监督员。", "loadError": "无法加载代理监督员列表。请重试。", "loading": "正在加载代理监督员列表...", "refreshing": "正在刷新数据..."}, "card": {"model": "模型", "provider": "提供商", "active": "活跃", "inactive": "非活跃", "activate": "激活", "deactivate": "停用", "edit": "编辑", "delete": "删除", "restore": "恢复", "confirmDelete": "确认删除代理监督员", "deleteMessage": "您确定要删除此代理监督员吗？此操作无法撤销。", "deleteSuccess": "代理监督员删除成功", "deleteError": "删除代理监督员时发生错误", "updateSuccess": "代理监督员更新成功", "updateError": "更新代理监督员时发生错误", "restoreSuccess": "代理监督员恢复成功", "restoreError": "恢复代理监督员时发生错误"}, "form": {"basicInfo": "基本信息", "name": "代理监督员名称", "namePlaceholder": "输入代理监督员名称", "description": "描述", "descriptionPlaceholder": "输入代理监督员描述", "instruction": "指令", "instructionPlaceholder": "输入代理监督员指令", "avatar": "头像", "avatarUpload": "上传头像", "avatarHelp": "支持格式：JPG、PNG（仅1张图片）", "currentAvatar": "当前头像", "currentAvatarNote": "上传新图片以替换", "provider": "提供商类型", "selectProvider": "选择提供商", "resources": "资源", "model": "模型", "selectModel": "选择模型", "selectModelFirst": "请先选择模型以配置参数", "modelConfig": "模型配置", "temperature": "温度", "topP": "Top P", "topK": "Top K", "maxTokens": "最大令牌数", "mcpSystems": "MCP系统", "knowledgeFiles": "知识文件", "create": "创建代理监督员", "update": "更新代理监督员", "creating": "正在创建代理监督员...", "updating": "正在更新代理监督员...", "createSuccess": "代理监督员创建成功", "createError": "创建代理监督员时发生错误", "updateSuccess": "代理监督员更新成功", "updateError": "更新代理监督员时发生错误", "uploadingAvatar": "正在上传头像...", "uploadAvatarSuccess": "头像上传成功", "uploadAvatarError": "上传头像时发生错误", "loadSystemModelsError": "无法加载系统模型列表", "validation": {"nameRequired": "代理监督员名称是必需的", "descriptionRequired": "描述是必需的", "instructionRequired": "指令是必需的", "modelRequired": "模型是必需的"}}, "fileConfig": {"title": "知识文件", "loadingFiles": "正在加载知识文件...", "noFilesSelected": "尚未选择知识文件", "addFile": "添加知识文件", "removeFile": "移除知识文件", "confirmDeleteFile": "您确定要从代理监督员中移除知识文件\"{{fileName}}\"吗？", "deleteFileWarning": "此操作无法撤销。"}, "fileSlideIn": {"title": "选择知识文件", "fileName": "文件名", "extension": "格式", "storage": "大小", "createdAt": "创建日期", "save": "保存", "cancel": "取消", "filterBy": "筛选", "all": "全部", "updateFilesSuccess": "知识文件更新成功", "updateFilesError": "无法更新知识文件", "addFilesToListSuccess": "知识文件已成功添加到列表", "cannotSaveInThisMode": "在此模式下无法保存"}, "trash": {"title": "回收站 - 代理监督员", "noSupervisors": "回收站中没有代理监督员", "noSupervisorsDescription": "回收站为空。已删除的代理监督员将显示在这里。", "noSearchResults": "未找到符合条件的代理监督员", "loadError": "无法加载已删除的代理监督员列表"}, "mcpConfig": {"title": "MCP系统", "loadingMCPSystems": "正在加载MCP系统...", "noMCPSystemsSelected": "尚未选择MCP系统", "addMCPSystem": "添加MCP系统", "removeMCPSystem": "移除MCP系统", "confirmDeleteMCPSystem": "您确定要从代理监督员中移除MCP系统\"{{mcpSystemName}}\"吗？", "deleteMCPSystemWarning": "此操作无法撤销。"}}, "fileConfig": {"title": "知识文件", "loadingFiles": "正在加载知识文件...", "noFilesSelected": "尚未选择知识文件", "addFile": "添加知识文件", "removeFile": "移除知识文件"}, "trash": {"title": "回收站 - 代理类型", "noAgents": "回收站中没有代理类型", "noAgentsDescription": "回收站为空。已删除的代理类型将显示在这里。", "restoreAgent": "恢复代理类型", "permanentDelete": "永久删除"}, "list": {"title": "代理类型列表", "noTypes": "没有代理类型", "noTypesDescription": "系统中目前没有代理类型。", "loadError": "无法加载代理类型列表。请重试。", "loading": "正在加载代理类型列表...", "refreshing": "正在刷新数据..."}, "card": {"edit": "编辑", "delete": "删除", "cancel": "取消", "confirmDelete": "确认删除代理类型", "deleteMessage": "您确定要删除此代理类型吗？此操作无法撤销。", "deleteSuccess": "代理类型删除成功", "deleteError": "删除代理类型时发生错误", "updateSuccess": "代理类型更新成功", "updateError": "更新代理类型时发生错误"}, "form": {"basicInfo": "基本信息", "name": "代理类型名称", "nameCode": "标识符代码", "nameCodePlaceholder": "输入标识符代码", "instruction": "指令", "instructionPlaceholder": "输入代理类型指令", "avatar": "头像", "avatarUpload": "上传头像", "avatarHelp": "支持格式：JPG、PNG（仅限单张图片）", "currentAvatar": "当前头像", "currentAvatarNote": "上传新图片以替换", "modelConfig": "模型配置", "temperature": "温度", "topP": "Top P", "topK": "Top K", "maxTokens": "最大令牌数", "provider": "提供商类型", "resources": "资源", "model": "模型", "selectModel": "选择模型", "vectorStore": "向量存储", "selectVectorStore": "选择向量存储", "namePlaceholder": "输入代理类型名称", "description": "描述", "descriptionPlaceholder": "输入代理类型描述", "defaultConfig": "默认配置", "enableProfileCustomization": "启用代理配置文件自定义", "enableOutputMessenger": "启用输出到Messenger", "enableOutputLivechat": "启用输出到网站实时聊天", "enableConvert": "启用任务转换跟踪", "enableResources": "启用资源使用", "enableStrategy": "启用动态策略执行", "enableMultiAgent": "启用多代理协作", "enableOutputZaloOa": "启用输出到Zalo OA", "enableOutputPayment": "启用输出到支付", "enableTool": "启用工具使用", "enableShipment": "启用物流输出", "modelConfiguration": "模型配置", "isAllModel": "使用系统中所有模型", "registryConfig": {"title": "模型注册表", "loadingRegistries": "正在加载模型注册表...", "removeRegistry": "移除模型注册表", "noRegistriesSelected": "尚未选择模型注册表", "addRegistry": "添加模型注册表", "confirmDeleteRegistry": "您确定要从代理类型中移除模型注册表\"{{registryName}}\"吗？", "deleteRegistryWarning": "此操作无法撤销。"}, "modelRegistrySlideIn": {"title": "选择模型注册表", "save": "保存", "cancel": "取消", "model": "模型", "modelNamePattern": "模型模式", "provider": "提供商", "cannotSaveInThisMode": "无法在此模式下保存", "updateModelsSuccess": "模型更新成功", "updateModelsError": "更新模型失败", "addModelsToListSuccess": "模型已成功添加到列表", "filterBy": "筛选条件", "all": "全部", "noModelsFound": "未找到模型", "loadingModels": "正在加载模型...", "searchPlaceholder": "搜索模型...", "selectModels": "选择模型", "selectedCount": "已选择 {{count}} 个模型"}, "status": "状态", "selectStatus": "选择状态", "draft": "草稿", "approved": "已批准", "agentSystems": "代理系统", "selectAgentSystems": "选择代理系统", "agentSystemsConfig": {"title": "代理系统", "noSystemsSelected": "未选择任何代理系统", "systemCount": "已选择 {{count}} 个代理系统", "addSystem": "添加代理系统", "selectedSystems": "已选择的代理系统", "removeSystem": "移除代理系统", "removeSystemSuccess": "代理系统移除成功", "removeSystemError": "移除代理系统时出错", "confirmDeleteSystem": "您确定要从此代理类型中移除代理系统 \"{{systemName}\" 吗？", "deleteSystemWarning": "此操作无法撤销。", "createdAt": "创建时间"}, "agentSystemSlideIn": {"title": "选择代理系统", "close": "关闭", "cancel": "取消", "save": "保存", "system": "系统", "status": "状态", "active": "活跃", "inactive": "非活跃", "createdAt": "创建时间", "filterBy": "筛选", "all": "全部", "updateSystemsSuccess": "代理系统更新成功", "updateSystemsError": "更新代理系统时出错", "addSystemsToListSuccess": "代理系统添加到列表成功", "cannotSaveInThisMode": "无法在此模式下保存"}, "create": "创建代理类型", "creating": "正在创建代理类型...", "createSuccess": "代理类型创建成功", "createError": "创建代理类型时发生错误", "loadAgentSystemsError": "无法加载代理系统列表"}, "validation": {"nameRequired": "代理类型名称是必需的", "descriptionRequired": "描述是必需的", "statusRequired": "状态是必需的", "agentSystemsRequired": "至少需要一个代理系统"}, "common": {"confirmDelete": "确认删除", "cancel": "取消", "delete": "删除", "error": "错误", "locale": "zh-CN", "success": "成功", "loading": "加载中...", "save": "保存", "close": "关闭", "edit": "编辑", "view": "查看", "back": "返回", "next": "下一步", "previous": "上一步", "search": "搜索", "filter": "筛选", "all": "全部", "active": "活跃", "inactive": "非活跃", "draft": "草稿", "approved": "已批准", "create": "创建", "update": "更新", "refresh": "刷新", "restore": "恢复"}, "deleteConfirmTitle": "确认删除代理类型", "deleteConfirmMessage": "您确定要删除此代理类型吗？此操作将把代理类型移至回收站，可以恢复。", "deleteSuccess": "删除成功", "deleteSuccessMessage": "代理类型已成功删除", "deleteError": "删除代理类型时发生错误", "selectTypeToDelete": "选择要删除的代理类型", "deleteWithMigration": "删除并迁移", "deleteWithMigrationDescription": "删除代理类型并将此类型的所有代理迁移到新选择的类型。", "newTypeAgent": "新代理类型", "selectNewType": "选择新代理类型", "selectNewTypeDescription": "选择新代理类型以迁移当前代理。", "noAvailableTypes": "没有其他代理类型可用于迁移。您只能删除而不迁移。", "deleteOnly": "仅删除"}, "card": {"supervisor": "监督员", "active": "活跃", "inactive": "非活跃", "activate": "激活", "deactivate": "停用", "edit": "编辑", "delete": "删除", "restore": "恢复", "memories": "记忆", "confirmDelete": "确认删除代理", "deleteMessage": "您确定要删除此代理吗？此操作无法撤销。", "deleteSuccess": "代理删除成功", "deleteError": "删除代理时发生错误", "updateSuccess": "代理更新成功", "updateError": "更新代理时发生错误", "setSupervisor": "设为监督员", "removeSupervisor": "移除监督员", "setSupervisorSuccess": "成功设为监督员", "removeSupervisorSuccess": "成功移除监督员权限", "supervisorError": "更改监督员权限时发生错误", "restoreSuccess": "代理恢复成功", "restoreError": "恢复代理时发生错误"}, "trash": {"noAgents": "回收站中没有代理", "noAgentsDescription": "回收站为空。已删除的代理将出现在这里。"}, "edit": {"notFound": "未找到代理"}, "pagination": {"itemsPerPage": "每页项目数", "showingItems": "显示 {from} - {to} 共 {total} 项", "page": "页", "of": "共", "previous": "上一页", "next": "下一页"}, "form": {"basicInfo": "基本信息", "name": "代理名称", "namePlaceholder": "输入代理名称", "nameCode": "标识符代码", "nameCodePlaceholder": "输入标识符代码", "instruction": "指令", "instructionPlaceholder": "输入代理指令", "description": "描述", "descriptionPlaceholder": "输入代理描述", "avatar": "头像", "avatarUpload": "上传头像", "avatarHelp": "支持格式：JPG、PNG（仅限单张图片）", "modelConfig": "模型配置", "temperature": "温度", "topP": "Top P", "topK": "Top K", "maxTokens": "最大令牌数", "provider": "提供商类型", "resources": "资源", "model": "模型", "selectModel": "选择模型", "vectorStore": "向量存储", "selectVectorStore": "选择向量存储", "mcpSystems": "MCP系统", "isSupervisor": "是监督员", "create": "创建代理", "creating": "正在创建代理...", "createSuccess": "代理创建成功", "createError": "创建代理时发生错误", "uploadingAvatar": "正在上传头像...", "uploadAvatarSuccess": "头像上传成功", "uploadAvatarError": "上传头像时发生错误", "error": "错误", "errorMessage": "创建代理时发生错误", "updateError": "更新代理时发生错误"}, "upload": {"success": "上传成功", "successMessage": "所有文件上传成功", "error": "上传错误", "errorMessage": "上传文件时发生错误"}, "validation": {"nameRequired": "代理名称是必需的", "nameCodeRequired": "标识符代码是必需的", "nameCodeFormat": "标识符代码只能包含小写字母、数字、下划线和连字符", "instructionRequired": "指令是必需的", "descriptionRequired": "描述是必需的", "modelRequired": "模型是必需的", "modelIdInvalid": "模型ID必须是有效的UUID"}, "mcp": {"config": {"title": "MCP 系统", "description": "管理此代理类型的MCP系统", "addMCPSystem": "添加MCP系统", "noMCPSystems": "暂无MCP系统", "removeMCPSystem": "移除MCP系统", "mcpSystemName": "MCP系统名称", "mcpSystemDescription": "MCP系统描述"}, "slideIn": {"title": "选择MCP系统", "save": "保存", "cancel": "取消", "noChangesToSave": "没有更改需要保存", "selectAtLeastOne": "至少选择一个MCP系统", "search": "搜索MCP系统...", "noResults": "未找到MCP系统", "selectAll": "全选", "deselectAll": "取消全选", "selected": "已选择 {{count}} 个MCP系统", "hasChanges": "有更改", "addMCPsToListSuccess": "MCP系统已成功添加到列表", "updateMCPsSuccess": "MCP系统更新成功", "updateMCPsError": "更新MCP系统时发生错误", "columns": {"nameServer": "服务器名称", "description": "描述", "updatedAt": "更新时间"}}, "strategy": {"editStrategy": "编辑策略", "validation": {"nameRequired": "策略名称是必需的", "instructionRequired": "指令是必需的", "modelRequired": "模型是必需的", "contentRequired": "步骤内容是必需的", "exampleRequired": "默认示例是必需的"}, "form": {"basicInfo": "基本信息", "name": "策略名称", "namePlaceholder": "输入策略名称", "instruction": "指令", "instructionPlaceholder": "输入策略指令", "avatar": "头像", "currentAvatar": "当前头像", "avatarUpload": "上传头像", "avatarHelp": "支持格式：JPG、PNG（仅1张图片）", "model": "模型", "selectModel": "选择模型", "modelConfig": "模型配置", "selectModelFirst": "请选择模型以配置参数", "content": "步骤内容", "addStep": "添加步骤", "step": "步骤", "contentPlaceholder": "输入步骤{{step}}的内容", "exampleDefault": "默认示例", "addExample": "添加示例", "example": "示例", "examplePlaceholder": "输入步骤{{step}}的示例", "update": "更新", "uploadingAvatar": "正在上传头像...", "uploadAvatarSuccess": "头像上传成功", "uploadAvatarError": "上传头像时出错", "updateSuccess": "策略更新成功", "updateError": "更新策略时出错"}}}, "common": {"confirmDelete": "确认删除"}, "memories": {"title": "已保存的记忆", "addNew": "添加新记忆", "addNewMemory": "添加新记忆", "editMemory": "编辑记忆", "titleLabel": "标题", "titlePlaceholder": "输入记忆标题", "contentLabel": "内容", "contentPlaceholder": "输入记忆内容", "reasonLabel": "原因", "reasonPlaceholder": "输入保存此记忆的原因", "edit": "编辑", "delete": "删除", "empty": "尚未保存任何记忆"}, "type": {"editType": "编辑代理类型", "updateType": "更新代理类型", "validation": {"nameRequired": "代理类型名称是必需的", "descriptionRequired": "描述是必需的"}, "form": {"basicInfo": "基本信息", "name": "代理类型名称", "namePlaceholder": "输入代理类型名称", "description": "描述", "descriptionPlaceholder": "输入代理类型描述", "defaultConfig": "默认配置", "enableProfileCustomization": "允许代理配置文件自定义", "enableOutputMessenger": "允许输出到Messenger", "enableOutputLivechat": "允许输出到网站实时聊天", "enableConvert": "跟踪任务转换", "enableResources": "使用资源", "enableStrategy": "执行动态策略", "enableMultiAgent": "多代理协作", "enableOutputZaloOa": "允许输出到Zalo OA", "enableOutputPayment": "允许输出到支付", "enableTool": "允许输出到工具", "enableShipment": "允许输出到发货"}, "common": {"cancel": "取消", "delete": "删除"}}}