import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { Button, Icon, Typography, Modal } from '@/shared/components/common';
import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { useAdminAgentNotification } from '../hooks/useAdminAgentNotification';
import ModelRegistrySlideInForm, { ModelRegistry } from './ModelRegistrySlideInForm';
import { adminTypeAgentService } from '../agent-type/services/type-agent.service';
import { useRemoveModel } from '../agent-type/hooks/useTypeAgent';
import { AdminConfigWrapper } from './AdminConfigWrapper';

/**
 * Interface cho dữ liệu Registry Config
 */
export interface RegistryConfigData {
  modelRegistryIds: string[];
}

interface RegistryConfigProps {
  initialData?: RegistryConfigData;
  onSave?: (data: RegistryConfigData) => void;
  mode?: 'create' | 'edit';
  agentTypeId?: string | undefined;
}

/**
 * API function để lấy thông tin chi tiết của Model Registries
 */
const getModelRegistriesByIds = async (
  ids: string[]
): Promise<ApiResponseDto<ModelRegistry[]>> => {
  if (ids.length === 0) {
    return { code: 200, message: 'Success', result: [] };
  }

  // Gọi API để lấy tất cả model registries và filter theo IDs
  const response = await apiClient.get<{ items: ModelRegistry[] }>(`/admin/model-registry?limit=100`);
  const allRegistries = response.result.items || [];
  const filteredRegistries = allRegistries.filter((registry: ModelRegistry) =>
    ids.includes(registry.id)
  );

  return {
    code: 200,
    message: 'Success',
    result: filteredRegistries
  };
};

const RegistryConfig: React.FC<RegistryConfigProps> = ({
  initialData,
  onSave,
  mode = 'create',
  agentTypeId
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const {
    deleteSuccess,
    deleteError
  } = useAdminAgentNotification();

  // Ref để track việc đã load data chưa
  const loadedRef = useRef(false);
  const agentTypeIdRef = useRef<string | null>(null);

  // Hooks for API operations (only in edit mode)
  const agentTypeIdNum = agentTypeId ? parseInt(agentTypeId) : 0;
  const removeModelMutation = useRemoveModel(agentTypeIdNum);

  // State cho dữ liệu config - khởi tạo empty
  const [configData, setConfigData] = useState<RegistryConfigData>({
    modelRegistryIds: [],
  });

  // Initialize data chỉ 1 lần khi props thay đổi
  useEffect(() => {
    // Nếu đã load cho agentTypeId này rồi thì skip
    if (agentTypeIdRef.current === agentTypeId && loadedRef.current) {
      return;
    }

    const loadInitialData = async () => {
      console.log('🚀 RegistryConfig - Loading initial data:', { mode, agentTypeId, initialData });

      // Update refs
      agentTypeIdRef.current = agentTypeId || null;
      loadedRef.current = true;

      // Nếu có initialData và không empty thì dùng luôn
      if (initialData && initialData.modelRegistryIds.length > 0) {
        console.log('📋 Using provided initialData:', initialData);
        setConfigData(initialData);
        return;
      }

      // Chỉ load từ API nếu mode edit và có agentTypeId
      if (mode === 'edit' && agentTypeId) {
        try {
          console.log('📡 Calling API to get models...');
          const existingModelIds = await adminTypeAgentService.getModels(parseInt(agentTypeId));

          const loadedData: RegistryConfigData = {
            modelRegistryIds: existingModelIds,
          };

          console.log('✅ API Response:', loadedData);
          setConfigData(loadedData);

          // Gọi callback để cập nhật parent component
          if (onSave) {
            onSave(loadedData);
          }
        } catch (error) {
          console.error('❌ Error loading initial model registries data:', error);
        }
      } else {
        // Mode create hoặc không có agentTypeId
        setConfigData({
          modelRegistryIds: [],
        });
      }
    };

    loadInitialData();
  }, [agentTypeId]); // Chỉ depend vào agentTypeId

  // State cho form slide-in
  const [showRegistryForm, setShowRegistryForm] = useState(false);
  const [removingRegistryId, setRemovingRegistryId] = useState<string | null>(null);

  // State cho modal xác nhận xóa (chỉ dùng ở mode edit)
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [registryToDelete, setRegistryToDelete] = useState<ModelRegistry | null>(null);

  // Cập nhật dữ liệu khi initialData thay đổi
  useEffect(() => {
    if (initialData) {
      setConfigData(initialData);
    }
  }, [initialData]);

  // Memoize modelRegistryIds để tránh re-render liên tục
  const memoizedModelRegistryIds = useMemo(() => configData.modelRegistryIds, [configData.modelRegistryIds]);

  // Query để lấy thông tin chi tiết của các registries đã chọn
  const {
    data: selectedRegistriesResponse,
    isLoading: isLoadingSelectedRegistries,
    refetch: refetchSelectedRegistries
  } = useQuery({
    queryKey: ['selected-model-registries', memoizedModelRegistryIds],
    queryFn: () => getModelRegistriesByIds(memoizedModelRegistryIds),
    enabled: memoizedModelRegistryIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false, // Tránh re-fetch khi focus window
    refetchOnMount: false, // Tránh re-fetch khi mount lại
  });

  const selectedRegistries = selectedRegistriesResponse?.result || [];

  // Xử lý khi lưu từ ModelRegistrySlideInForm
  const handleRegistryFormSave = (selectedRegistryIds: string[]) => {
    const newConfigData: RegistryConfigData = {
      modelRegistryIds: selectedRegistryIds,
    };

    setConfigData(newConfigData);

    // Gọi callback để cập nhật parent component
    if (onSave) {
      onSave(newConfigData);
    }

    // Refetch selected registries để cập nhật UI
    if (selectedRegistryIds.length > 0) {
      refetchSelectedRegistries();
    }
  };

  // Xử lý khi click vào icon xóa registry
  const handleRemoveClick = (registry: ModelRegistry) => {
    if (mode === 'edit') {
      // Mode edit: Hiển thị modal xác nhận
      setRegistryToDelete(registry);
      setShowDeleteModal(true);
    } else {
      // Mode create: Xóa trực tiếp
      handleRemoveRegistry(registry.id);
    }
  };

  // Xử lý xóa registry
  const handleRemoveRegistry = async (registryId: string) => {
    if (mode === 'edit' && agentTypeId) {
      // Mode edit: Gọi API để cập nhật model registries
      setRemovingRegistryId(registryId);
      try {
        const newRegistryIds = configData.modelRegistryIds.filter(id => id !== registryId);

        // Gọi DELETE API
        await removeModelMutation.mutateAsync(registryId);

        // Cập nhật local state
        const newConfigData: RegistryConfigData = {
          modelRegistryIds: newRegistryIds,
        };
        setConfigData(newConfigData);

        // Gọi callback để cập nhật parent component
        if (onSave) {
          onSave(newConfigData);
        }

        deleteSuccess('Model Registry');

      } catch (error) {
        deleteError('Model Registry', error instanceof Error ? error.message : undefined);
      } finally {
        setRemovingRegistryId(null);
      }
    } else {
      // Mode create: Chỉ cập nhật local state
      const newRegistryIds = configData.modelRegistryIds.filter(id => id !== registryId);
      const newConfigData: RegistryConfigData = {
        modelRegistryIds: newRegistryIds,
      };

      setConfigData(newConfigData);

      // Gọi callback để cập nhật parent component
      if (onSave) {
        onSave(newConfigData);
      }

      deleteSuccess('Model Registry');
    }
  };

  // Xử lý xác nhận xóa từ modal
  const handleConfirmDelete = async () => {
    if (registryToDelete) {
      await handleRemoveRegistry(registryToDelete.id);
      setShowDeleteModal(false);
      setRegistryToDelete(null);
    }
  };

  // Xử lý hủy xóa
  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setRegistryToDelete(null);
  };

  // Handlers
  const handleShowAddForm = useCallback(() => {
    setShowRegistryForm(true);
  }, []);

  const handleCloseRegistryForm = useCallback(() => {
    setShowRegistryForm(false);
  }, []);

  return (
    <AdminConfigWrapper
      title={
        <div className="flex items-center">
          <Icon name="cpu" size="md" className="mr-2" />
          <span>{t('admin:agent.form.registryConfig.title', 'Model Registry')}</span>
        </div>
      }
    >
      <div>
       

        <div className="space-y-3">
          {(isLoadingSelectedRegistries) ? (
            <div className="text-center py-4 text-gray-500 dark:text-gray-400">
              <Icon name="loader-circle" size="md" className="animate-spin mx-auto mb-2" />
              {t('admin:agent.form.registryConfig.loadingRegistries', 'Loading model registries...')}
            </div>
          ) : selectedRegistries.length > 0 ? (
            <div className={`space-y-3 ${selectedRegistries.length > 8 ? 'max-h-96 overflow-y-auto pr-2' : ''}`}>
              {selectedRegistries.map(registry => (
                <div
                  key={registry.id}
                  className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-purple-50 dark:bg-purple-900/10 hover:shadow-sm transition-all"
                >
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <div className="w-10 h-10 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-pink-600 flex-shrink-0 flex items-center justify-center">
                      <Icon name="cpu" size="sm" className="text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Typography variant="body2" className="font-semibold truncate text-gray-900 dark:text-gray-100">
                          {registry.modelBaseId}
                        </Typography>
                        <span className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full">
                          {registry.provider}
                        </span>
                      </div>
                      <Typography variant="caption" className="text-gray-500 dark:text-gray-400">
                        Pattern: {registry.modelBaseId}
                      </Typography>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveClick(registry)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 p-2 rounded-full transition-all duration-200"
                    disabled={removingRegistryId === registry.id}
                    title={t('admin:agent.form.registryConfig.removeRegistry', 'Remove Model Registry')}
                  >
                    {removingRegistryId === registry.id ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-red-500 border-t-transparent"></div>
                    ) : (
                      <Icon name="trash" size="sm" />
                    )}
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
              {t('admin:agent.form.registryConfig.noRegistriesSelected', 'No model registries selected')}
            </div>
          )}
        </div>

        {/* Nút thêm registry */}
        <div className='flex flex-col items-center pt-6 space-y-4'>
          <Button variant="outline" size="sm" onClick={handleShowAddForm}>
            <Icon name="plus" size="sm" className="mr-1" />
            {t('admin:agent.form.registryConfig.addRegistry', 'Add Model Registry')}
          </Button>
        </div>
      </div>

      {/* Model Registry Slide-in Form */}
      <ModelRegistrySlideInForm
        isVisible={showRegistryForm}
        onClose={handleCloseRegistryForm}
        onSave={handleRegistryFormSave}
        agentTypeId={agentTypeId}
        mode={mode}
        initialSelectedIds={configData.modelRegistryIds}
      />

      {/* Modal xác nhận xóa - chỉ hiển thị ở mode edit */}
      {mode === 'edit' && (
        <Modal
          isOpen={showDeleteModal}
          onClose={handleCancelDelete}
          title={t('admin:agent.common.confirmDelete', 'Xác nhận xóa')}
          size="md"
          footer={
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={handleCancelDelete}>
                {t('admin:agent.type.common.cancel', 'Hủy')}
              </Button>
              <Button
                variant="danger"
                onClick={handleConfirmDelete}
                isLoading={removingRegistryId === registryToDelete?.id}
                disabled={removingRegistryId === registryToDelete?.id}
              >
                {t('admin:agent.type.common.delete', 'Xóa')}
              </Button>
            </div>
          }
        >
          <div className="py-4">
            <Typography className="mb-4">
              {t(
                'admin:agent.form.registryConfig.confirmDeleteRegistry',
                'Bạn có chắc chắn muốn xóa model registry "{{registryName}}" khỏi loại agent không?',
                { registryName: registryToDelete?.modelBaseId || '' }
              )}
            </Typography>
            <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
              {t(
                'admin:agent.form.registryConfig.deleteRegistryWarning',
                'Hành động này không thể hoàn tác.'
              )}
            </Typography>
          </div>
        </Modal>
      )}
    </AdminConfigWrapper>
  );
};

export default React.memo(RegistryConfig);
