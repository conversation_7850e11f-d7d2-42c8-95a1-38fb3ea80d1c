{"agent": {"management": {"title": "Agent Management", "description": "Comprehensive Agent system management"}, "notification": {"createSuccess": "{{entityName}} has been created successfully", "updateSuccess": "{{entityName}} has been updated successfully", "deleteSuccess": "{{entityName}} has been deleted successfully", "restoreSuccess": "{{entityName}} has been restored successfully", "createError": "An error occurred while creating {{entityName}}", "updateError": "An error occurred while updating {{entityName}}", "deleteError": "An error occurred while deleting {{entityName}}", "restoreError": "An error occurred while restoring {{entityName}}", "loadError": "Unable to load {{entityName}} list", "uploadSuccess": "Upload successful", "uploadSuccessWithName": "{{fileName}} uploaded successfully", "uploadError": "An error occurred while uploading", "validationError": "Invalid data", "permissionError": "You do not have permission to perform this action", "networkError": "Network connection error. Please try again", "processing": "{{action}}..."}, "rank": {"title": "Agent Rank Management", "description": "Agent ranking management", "pageTitle": "Agent Rank Management", "addRank": "Add New Rank", "editRank": "Edit Agent Rank", "searchPlaceholder": "Search Ranks...", "noSearchResults": "No Ranks found matching your search criteria.", "createFirst": "Create First Agent Rank", "sortBy": "Sort by", "createSuccess": "Success", "createSuccessMessage": "Agent rank has been created successfully", "updateSuccess": "Success", "updateSuccessMessage": "Agent rank has been updated successfully", "active": "Active", "inactive": "Inactive", "editAction": "Edit", "deleteAction": "Delete", "confirmDelete": "Confirm Delete Rank", "deleteMessage": "Are you sure you want to delete this rank? This action cannot be undone.", "deleteSuccess": "Rank deleted successfully", "deleteError": "An error occurred while deleting the rank", "memories": "Memories", "list": {"title": "Rank List", "noRanks": "No Ranks", "noRanksDescription": "There are currently no ranks in the system.", "loadError": "Unable to load rank list. Please try again.", "loading": "Loading rank list...", "refreshing": "Refreshing data..."}, "form": {"basicInfo": "Basic Information", "name": "Rank Name", "namePlaceholder": "Enter rank name", "description": "Description", "descriptionPlaceholder": "Enter rank description", "expRange": "Experience Range", "minExp": "Minimum Experience", "maxExp": "Maximum Experience", "badge": "Badge", "badgeUpload": "Upload badge", "badgeHelp": "Supported formats: JPG, PNG (single image only)", "currentBadge": "Current Badge", "currentBadgeNote": "Upload new image to replace", "active": "Active", "create": "Create Rank", "update": "Update", "creating": "Creating Rank...", "createSuccess": "Rank created successfully", "createError": "An error occurred while creating the rank", "updateError": "An error occurred while updating the rank"}, "validation": {"nameRequired": "Rank name is required", "descriptionRequired": "Description is required", "minExpInvalid": "Minimum experience must be >= 0", "minExpInteger": "Minimum experience must be an integer", "maxExpInvalid": "Maximum experience must be > 0", "maxExpInteger": "Maximum experience must be an integer", "expRangeInvalid": "Maximum experience must be greater than minimum experience", "expRangeOverlap": "Experience range overlaps with another rank's experience range"}, "edit": {"notFound": "Rank not found"}, "sort": {"name": "Name", "minExp": "Minimum Experience", "maxExp": "Maximum Experience", "createdAt": "Created Date"}}, "system": {"mcpSystem": {"title": "MCP Systems", "pageTitle": "MCP System Management", "pageDescription": "Manage Model Context Protocol systems for your AI agents", "subtitle": "Manage your Model Context Protocol systems", "loading": "Loading MCP Systems...", "noMCPSystems": "No MCP Systems", "noMCPSystemsDescription": "No MCP systems have been created yet. Create your first MCP system to get started.", "addFirst": "Add First MCP System", "addNew": "Add MCP System", "edit": "Edit", "delete": "Delete", "testConnection": "Test Connection", "configInvalid": "Config is invalid for testing", "transport": "Transport", "updated": "Updated", "by": "By", "confirmDelete": "Confirm Delete", "deleteWarning": "Are you sure you want to delete this MCP System? This action cannot be undone.", "deleteSuccess": "MCP System deleted successfully", "deleteError": "Failed to delete MCP System", "testSuccess": "Connection test successful", "testError": "Connection test failed", "totalSystems": "Total Systems", "httpSystems": "HTTP Systems", "sseSystems": "SSE Systems", "searchPlaceholder": "Search MCP systems...", "manage": "Manage", "systemMCPTitle": "System MCP", "systemMCPSubtitle": "Admin MCP system management endpoints", "form": {"addDescription": "Create a new Model Context Protocol system", "basicInfo": "Basic Information", "nameServer": "Server Name", "nameServerPlaceholder": "Enter MCP server name", "description": "Description", "descriptionPlaceholder": "Enter description for MCP system", "config": "Configuration", "transport": "Transport Protocol", "selectTransport": "Select transport protocol", "httpDescription": "Standard HTTP protocol", "sseDescription": "Server-Sent Events", "url": "URL", "urlPlaceholder": "Enter MCP server URL", "headers": "Headers", "addHeader": "<PERSON>d <PERSON>", "headerName": "Name", "headerValue": "Value", "headerNamePlaceholder": "Header name", "headerValuePlaceholder": "Header value", "removeHeader": "Remove header", "create": "Create MCP System", "update": "Update MCP System", "editDescription": "Update Model Context Protocol system configuration", "loading": "Loading MCP System details...", "validation": {"nameServerRequired": "Server name is required", "nameServerMinLength": "Server name must be at least 2 characters", "descriptionRequired": "Description is required", "transportRequired": "Transport protocol is required", "urlRequired": "URL is required", "urlInvalid": "Invalid URL format", "headerNameRequired": "Header name is required", "headerValueRequired": "Header value is required"}, "createSuccess": "MCP System created successfully", "createError": "Failed to create MCP System", "updateSuccess": "MCP System updated successfully", "updateError": "Failed to update MCP System", "loadError": "Failed to load MCP System details"}}, "title": "System Agent Management", "description": "System Agent management", "pageTitle": "System Agent Management", "addAgent": "Add New Agent", "editAgent": "Edit Agent System", "searchPlaceholder": "Search Agents...", "noSearchResults": "No Agents found matching your search criteria.", "createFirst": "Create First System Agent", "viewTrash": "View Trash", "backToMain": "Back to Main List", "cancel": "Cancel", "updateAgent": "Update Agent", "createSuccess": "Success", "createSuccessMessage": "Agent system has been created successfully", "updateSuccess": "Success", "updateSuccessMessage": "Agent system has been updated successfully", "mcpConfig": {"title": "MCP Systems", "loadingMCPSystems": "Loading MCP systems...", "noMCPSystemsSelected": "No MCP systems selected", "addMCPSystem": "Add MCP System", "removeMCPSystem": "Remove MCP System", "confirmDeleteMCPSystem": "Are you sure you want to remove MCP system \"{{mcpSystemName}}\" from the agent type?", "deleteMCPSystemWarning": "This action cannot be undone."}, "mcpSlideIn": {"title": "Select MCP Systems", "mcpSystem": "MCP System", "description": "Description", "updatedAt": "Updated", "save": "Save", "cancel": "Cancel", "filterBy": "Filter by", "all": "All", "updateMCPsSuccess": "MCP Systems updated successfully", "updateMCPsError": "Failed to update MCP Systems", "addMCPsToListSuccess": "MCP Systems added to list successfully", "cannotSaveInThisMode": "Cannot save in this mode"}}, "user": {"title": "Admin - Agent User", "description": "User Agent management"}, "type": {"title": "Agent Type Management", "description": "Agent Type management", "pageTitle": "Agent Type Management", "addType": "Add New Agent Type", "editType": "Edit Agent Type", "searchPlaceholder": "Search agent types...", "noSearchResults": "No agent types found matching your search criteria.", "createFirst": "Create First Agent Type", "createSuccess": "Success", "createSuccessMessage": "Agent type has been created successfully", "updateSuccess": "Success", "updateSuccessMessage": "Agent type has been updated successfully", "cancel": "Cancel", "updateType": "Update Agent Type", "viewTrash": "View Trash", "backToMain": "Back to Main List", "deleteConfirmTitle": "Confirm Delete Agent Type", "deleteConfirmMessage": "Are you sure you want to delete this agent type? This action will move the agent type to trash and can be restored.", "deleteSuccess": "Delete Successful", "deleteSuccessMessage": "Agent type has been deleted successfully", "restoreSuccess": "Restore Successful", "restoreSuccessMessage": "Agent type has been restored successfully", "selectTypeToDelete": "Select Agent Type to Delete", "selectTypeToDeleteDescription": "Select the agent type you want to delete. The agent type will be moved to trash and can be restored.", "deleteWithMigration": "Delete with Migration", "deleteWithMigrationDescription": "Delete agent type and migrate all agents of this type to the newly selected type.", "newTypeAgent": "New Agent Type", "selectNewType": "Select New Agent Type", "selectNewTypeDescription": "Select the new agent type to migrate current agents to.", "noAvailableTypes": "No other agent types available for migration. You can only delete without migration.", "deleteOnly": "Delete Only", "deleteError": "An error occurred while deleting agent type", "restoreError": "An error occurred while restoring agent type"}, "strategy": {"title": "Agent Strategy Management", "description": "Agent strategy management", "pageTitle": "Agent Strategy Management", "addStrategy": "Add New Strategy", "editStrategy": "Edit Agent Strategy", "searchPlaceholder": "Search strategies...", "noSearchResults": "No strategies found matching your search criteria.", "createFirst": "Create First Agent Strategy", "createSuccess": "Success", "createSuccessMessage": "Agent strategy has been created successfully", "updateSuccess": "Success", "updateSuccessMessage": "Agent strategy has been updated successfully", "cancel": "Cancel", "updateStrategy": "Update Strategy", "viewTrash": "View Trash", "backToMain": "Back to Main List", "list": {"title": "Strategy List", "noStrategies": "No Strategies", "noStrategiesDescription": "There are currently no strategies in the system.", "loadError": "Unable to load strategy list. Please try again.", "loading": "Loading strategy list...", "refreshing": "Refreshing data..."}, "card": {"edit": "Edit", "delete": "Delete", "restore": "Rest<PERSON>", "memories": "Memories", "confirmDelete": "Confirm Delete Strategy", "deleteMessage": "Are you sure you want to delete this strategy? This action cannot be undone.", "deleteSuccess": "Strategy deleted successfully", "deleteError": "An error occurred while deleting the strategy", "updateSuccess": "Strategy updated successfully", "updateError": "An error occurred while updating the strategy", "restoreSuccess": "Strategy restored successfully", "restoreError": "An error occurred while restoring the strategy"}, "form": {"basicInfo": "Basic Information", "name": "Strategy Name", "namePlaceholder": "Enter strategy name", "avatar": "Avatar", "avatarUpload": "Upload avatar", "avatarHelp": "Supported formats: JPG, PNG (single image only)", "modelConfig": "Model Configuration", "temperature": "Temperature", "maxTokens": "<PERSON>", "instruction": "Instructions", "instructionPlaceholder": "Enter instructions for strategy", "content": "Step Content", "contentStep": "Step {step}", "contentPlaceholder": "Enter content for step {step}", "addStep": "Add Step", "removeStep": "Remove Step", "exampleDefault": "Default Examples", "exampleStep": "Example {step}", "examplePlaceholder": "Enter example for step {step}", "addExample": "Add Example", "removeExample": "Remove Example", "systemModel": "System Model", "provider": "Provider Type", "selectProvider": "Select provider", "selectProviderFirst": "Please select provider first", "model": "Model", "selectSystemModel": "Select system model", "selectModel": "Select model", "create": "Create Strategy", "update": "Update Strategy", "creating": "Creating strategy...", "updating": "Updating strategy...", "createSuccess": "Strategy created successfully", "createError": "An error occurred while creating the strategy", "updateSuccess": "Strategy updated successfully", "updateError": "An error occurred while updating the strategy", "uploadingAvatar": "Uploading avatar...", "uploadAvatarSuccess": "Avatar uploaded successfully", "uploadAvatarError": "An error occurred while uploading avatar", "loadSystemModelsError": "Unable to load system models list", "currentAvatar": "Current Avatar"}, "trash": {"title": "Trash - Agent Strategies", "noStrategies": "No strategies in trash", "noStrategiesDescription": "Trash is empty. Deleted strategies will appear here.", "noSearchResults": "No strategies found matching your search criteria", "loadError": "Unable to load deleted strategies list"}, "validation": {"nameRequired": "Strategy name is required", "instructionRequired": "Instructions are required", "systemModelRequired": "System model is required", "providerRequired": "Please select a provider", "contentRequired": "Step content is required", "contentStepRequired": "Content for step {step} is required", "exampleRequired": "Default examples are required", "exampleStepRequired": "Example for step {step} is required"}}, "template": {"title": "Agent Template", "description": "Manage available agent templates", "pageTitle": "Agent Template Management", "noTemplates": "No templates yet", "noTemplatesDescription": "There are currently no templates in the system.", "noSearchResults": "No matching templates found", "loadError": "Unable to load template list. Please try again.", "loading": "Loading template list...", "refreshing": "Refreshing data...", "viewTrash": "View Trash", "backToMain": "Back to Main List", "selectType": "Select Agent Type", "selectTypeDescription": "Choose the agent type that suits your needs. Each agent type has different capabilities and characteristics.", "createTitle": "Create Agent", "editTitle": "Edit Agent Template", "create": {"title": "Create Agent Template"}, "profile": {"title": "Profile Information", "dateOfBirth": "Birth Date", "dateOfBirthPlaceholder": "Select date of birth", "gender": {"label": "Gender", "placeholder": "Select gender", "male": "Male", "female": "Female", "other": "Other"}, "education": {"label": "Education", "placeholder": "Select education level", "highSchool": "High School", "college": "College", "university": "University", "master": "Master", "phd": "PhD"}, "languages": "Language", "languagesPlaceholder": "Select language", "nations": "Country", "nationsPlaceholder": "Select country", "position": "Position", "positionPlaceholder": "Enter position", "skills": "Skills", "skillsPlaceholder": "Enter skill and press Enter", "personality": "Personality", "personalityPlaceholder": "Enter personality trait and press Enter"}, "memories": {"title": "Memories Configuration", "description": "Configure the memories that the agent will use to provide better responses and maintain context", "noMemories": "No memories configured", "addMemory": "Add Memory", "editMemory": "Edit Memory", "memoryTitle": "Title", "titlePlaceholder": "Enter memory title", "reason": "Reason", "reasonPlaceholder": "Enter reason for this memory", "content": "Content", "contentPlaceholder": "Enter memory content", "save": "Save Memory", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this memory?", "deleteSuccess": "Memory deleted successfully", "deleteError": "Error deleting memory", "saveSuccess": "Memory saved successfully", "saveError": "Error saving memory", "validation": {"titleRequired": "Memory title is required", "reasonRequired": "Reason is required", "contentRequired": "Memory content is required"}}, "conversion": {"title": "Conversion Configuration", "description": "Configure the fields that will be collected from users during conversations", "noFields": "No conversion fields configured", "addField": "Add Conversion Field", "editField": "Edit Conversion Field", "fieldName": "Field Name", "fieldNamePlaceholder": "Enter field name", "fieldDescription": "Description", "descriptionPlaceholder": "Enter field description", "fieldType": "Field Type", "dataType": "Data Type", "required": "Required", "active": "Active", "save": "Save Field", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this field?", "deleteSuccess": "Field deleted successfully", "deleteError": "Error deleting field", "saveSuccess": "Field saved successfully", "saveError": "Error saving field", "type": {"string": "String", "number": "Number", "boolean": "Boolean", "arrayString": "Array of Strings", "arrayNumber": "Array of Numbers", "enum": "Enum"}, "validation": {"nameRequired": "Field name is required", "descriptionRequired": "Description is required", "typeRequired": "Field type is required"}}, "name": "Agent Name", "namePlaceholder": "Enter agent name", "provider": "Provider", "model": "Model", "selectModel": "Select model", "strategy": "Strategy", "selectStrategy": "Select strategy", "modelConfig": "Model Configuration", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "instruction": "Instruction", "instructionPlaceholder": "Enter instructions for the model...", "createSuccess": "Agent template created successfully", "createError": "Failed to create agent template", "updateSuccess": "Agent template updated successfully", "updateError": "Failed to update agent template", "uploadingAvatar": "Uploading avatar...", "uploadAvatarSuccess": "Avatar uploaded successfully", "uploadAvatarError": "An error occurred while uploading avatar", "isForSale": "For Sale Support", "isForSaleDescription": {"enabled": "This agent supports sales activities and can be used for commercial purposes", "disabled": "This agent does not support sales, only for support and consultation purposes"}, "validation": {"nameRequired": "Agent name is required", "modelRequired": "Model selection is required", "strategyRequired": "Strategy selection is required"}, "card": {"model": "Model", "cancel": "Cancel", "forSale": "For Sale Support", "supported": "Supported", "notSupported": "Not Supported", "delete": "Delete", "restore": "Rest<PERSON>", "memories": "Memories", "confirmDelete": "Confirm Delete Template", "deleteMessage": "Are you sure you want to delete this template? This action cannot be undone.", "deleteSuccess": "Template deleted successfully", "deleteError": "An error occurred while deleting the template", "restoreSuccess": "Template restored successfully", "restoreError": "An error occurred while restoring the template"}, "trash": {"title": "Trash - Agent <PERSON>", "noTemplates": "No templates in trash", "noTemplatesDescription": "Trash is empty. Deleted templates will appear here.", "noSearchResults": "No matching templates found", "loadError": "Unable to load deleted template list"}}, "supervisor": {"title": "Agent Supervisor Management", "description": "Manage Agent Supervisors", "pageTitle": "Agent Supervisor Management", "addSupervisor": "Add New Agent Supervisor", "editSupervisor": "Edit Agent Supervisor", "searchPlaceholder": "Search Agent Supervisors...", "noSearchResults": "No Agent Supervisors found matching the search criteria.", "createFirst": "Create First Agent Supervisor", "viewTrash": "View Trash", "backToMain": "Back to Main List", "createSuccess": "Agent Supervisor created successfully", "updateSuccess": "Agent Supervisor updated successfully", "deleteSuccess": "Agent Supervisor deleted successfully", "restoreSuccess": "Agent Supervisor restored successfully", "createError": "Error occurred while creating Agent Supervisor", "updateError": "Error occurred while updating Agent Supervisor", "deleteError": "Error occurred while deleting Agent Supervisor", "restoreError": "Error occurred while restoring Agent Supervisor", "toggleActiveSuccess": "Status changed successfully", "toggleActiveError": "Error occurred while changing status", "list": {"title": "Agent Supervisor List", "noSupervisors": "No Agent Supervisors", "noSupervisorsDescription": "There are currently no Agent Supervisors in the system.", "loadError": "Unable to load Agent Supervisor list. Please try again.", "loading": "Loading Agent Supervisor list...", "refreshing": "Refreshing data..."}, "card": {"model": "Model", "provider": "Provider", "active": "Active", "inactive": "Inactive", "activate": "Activate", "deactivate": "Deactivate", "edit": "Edit", "delete": "Delete", "restore": "Rest<PERSON>", "confirmDelete": "Confirm Delete Agent Supervisor", "deleteMessage": "Are you sure you want to delete this Agent Supervisor? This action cannot be undone.", "deleteSuccess": "Agent Supervisor deleted successfully", "deleteError": "Error occurred while deleting Agent Supervisor", "updateSuccess": "Agent Supervisor updated successfully", "updateError": "Error occurred while updating Agent Supervisor", "restoreSuccess": "Agent Supervisor restored successfully", "restoreError": "Error occurred while restoring Agent Supervisor"}, "form": {"basicInfo": "Basic Information", "name": "Agent Supervisor Name", "namePlaceholder": "Enter Agent Supervisor name", "description": "Description", "descriptionPlaceholder": "Enter Agent Supervisor description", "instruction": "Instructions", "instructionPlaceholder": "Enter instructions for Agent Supervisor", "avatar": "Avatar", "avatarUpload": "Upload avatar", "avatarHelp": "Supported formats: JPG, PNG (1 image only)", "currentAvatar": "Current avatar", "currentAvatarNote": "Upload new image to replace", "provider": "Provider Type", "selectProvider": "Select provider", "resources": "Resources", "model": "Model", "selectModel": "Select model", "selectModelFirst": "Please select a model to configure parameters", "modelConfig": "Model Configuration", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "mcpSystems": "MCP Systems", "knowledgeFiles": "Knowledge Files", "create": "Create Agent Supervisor", "update": "Update Agent Supervisor", "creating": "Creating Agent Supervisor...", "updating": "Updating Agent Supervisor...", "createSuccess": "Agent Supervisor created successfully", "createError": "Error occurred while creating Agent Supervisor", "updateSuccess": "Agent Supervisor updated successfully", "updateError": "Error occurred while updating Agent Supervisor", "uploadingAvatar": "Uploading avatar...", "uploadAvatarSuccess": "Avatar uploaded successfully", "uploadAvatarError": "Error occurred while uploading avatar", "loadSystemModelsError": "Unable to load system models list", "validation": {"nameRequired": "Agent Supervisor name is required", "descriptionRequired": "Description is required", "instructionRequired": "Instructions are required", "modelRequired": "Model is required"}}, "fileConfig": {"title": "Knowledge Files", "loadingFiles": "Loading Knowledge Files...", "noFilesSelected": "No Knowledge Files selected yet", "addFile": "Add Knowledge File", "removeFile": "Remove Knowledge File", "confirmDeleteFile": "Are you sure you want to remove Knowledge File \"{{fileName}}\" from Agent Supervisor?", "deleteFileWarning": "This action cannot be undone."}, "fileSlideIn": {"title": "Select Knowledge Files", "fileName": "File Name", "extension": "Format", "storage": "Size", "createdAt": "Created Date", "save": "Save", "cancel": "Cancel", "filterBy": "Filter by", "all": "All", "updateFilesSuccess": "Knowledge Files updated successfully", "updateFilesError": "Unable to update Knowledge Files", "addFilesToListSuccess": "Knowledge Files added to list successfully", "cannotSaveInThisMode": "Cannot save in this mode"}, "trash": {"title": "Trash - Agent Supervisor", "noSupervisors": "No Agent Supervisors in trash", "noSupervisorsDescription": "Trash is empty. Deleted Agent Supervisors will appear here.", "noSearchResults": "No Agent Supervisors found matching criteria", "loadError": "Unable to load deleted Agent Supervisor list"}, "mcpConfig": {"title": "MCP Systems", "loadingMCPSystems": "Loading MCP Systems...", "noMCPSystemsSelected": "No MCP Systems selected yet", "addMCPSystem": "Add MCP System", "removeMCPSystem": "Remove MCP System", "confirmDeleteMCPSystem": "Are you sure you want to remove MCP system \"{{mcpSystemName}}\" from Agent Supervisor?", "deleteMCPSystemWarning": "This action cannot be undone."}}, "trash": {"title": "Trash - Agent Types", "noAgents": "No agent types in trash", "noAgentsDescription": "Trash is empty. Deleted agent types will appear here.", "restoreAgent": "Restore Agent Type", "permanentDelete": "Permanent Delete"}, "list": {"title": "Agent Type List", "noTypes": "No Agent Types", "noTypesDescription": "There are currently no agent types in the system.", "loadError": "Unable to load agent type list. Please try again.", "error": "Error", "retry": "Retry", "loading": "Loading agent type list...", "refreshing": "Refreshing data..."}, "card": {"edit": "Edit", "delete": "Delete", "confirmDelete": "Confirm Delete Agent Type", "deleteMessage": "Are you sure you want to delete this agent type? This action cannot be undone.", "deleteSuccess": "Agent type deleted successfully", "deleteError": "An error occurred while deleting the agent type", "updateSuccess": "Agent type updated successfully", "updateError": "An error occurred while updating the agent type"}, "form": {"basicInfo": "Basic Information", "name": "Agent Type Name", "nameCode": "Identifier Code", "nameCodePlaceholder": "Enter identifier code", "instruction": "Instructions", "instructionPlaceholder": "Enter instructions for agent type", "avatar": "Avatar", "avatarUpload": "Upload avatar", "avatarHelp": "Supported formats: JPG, PNG (single image only)", "currentAvatar": "Current Avatar", "currentAvatarNote": "Upload new image to replace", "modelConfig": "Model Configuration", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "provider": "Provider Type", "resources": "Resources", "model": "Model", "selectModel": "Select model", "vectorStore": "Vector Store", "selectVectorStore": "Select vector store", "namePlaceholder": "Enter agent type name", "description": "Description", "descriptionPlaceholder": "Enter agent type description", "defaultConfig": "Default Configuration", "enableProfileCustomization": "Enable Agent Profile Customization", "enableOutputMessenger": "Enable Output to Messenger", "enableOutputLivechat": "Enable Output to Website Live Chat", "enableConvert": "Enable Task Conversion Tracking", "enableResources": "Enable Resource Usage", "enableStrategy": "Enable Dynamic Strategy Execution", "enableMultiAgent": "Enable Multi-Agent Collaboration", "enableOutputZaloOa": "Enable Output to Zalo OA", "enableOutputPayment": "Enable Output to Payment", "enableTool": "Enable Tool Usage", "enableShipment": "Enable Shipment Output", "status": "Status", "selectStatus": "Select status", "draft": "Draft", "approved": "Approved", "agentSystems": "Agent Systems", "selectAgentSystems": "Select agent systems", "agentSystemsConfig": {"title": "Agent Systems", "noSystemsSelected": "No agent systems selected", "systemCount": "{{count}} agent systems selected", "addSystem": "Add Agent System", "selectedSystems": "Selected Agent Systems", "removeSystem": "Remove agent system", "removeSystemSuccess": "Agent system removed successfully", "removeSystemError": "Error removing agent system", "confirmDeleteSystem": "Are you sure you want to remove agent system \"{{systemName}\" from this agent type?", "deleteSystemWarning": "This action cannot be undone.", "createdAt": "Created At"}, "agentSystemSlideIn": {"title": "Select Agent System", "close": "Close", "cancel": "Cancel", "save": "Save", "system": "System", "status": "Status", "active": "Active", "inactive": "Inactive", "createdAt": "Created At", "filterBy": "Filter by", "all": "All", "updateSystemsSuccess": "Agent systems updated successfully", "updateSystemsError": "Error updating agent systems", "addSystemsToListSuccess": "Agent systems added to list successfully", "cannotSaveInThisMode": "Cannot save in this mode"}, "create": "Create Agent Type", "creating": "Creating agent type...", "createSuccess": "Agent type created successfully", "createError": "An error occurred while creating the agent type", "loadAgentSystemsError": "Unable to load agent systems list"}, "validation": {"nameRequired": "Agent type name is required", "descriptionRequired": "Description is required", "statusRequired": "Status is required", "agentSystemsRequired": "At least one agent system is required"}, "common": {"confirmDelete": "Confirm Delete", "cancel": "Cancel", "delete": "Delete", "error": "Error", "locale": "en-US", "success": "Success", "loading": "Loading...", "save": "Save", "close": "Close", "edit": "Edit", "view": "View", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "all": "All", "active": "Active", "inactive": "Inactive", "draft": "Draft", "approved": "Approved", "create": "Create", "update": "Update", "refresh": "Refresh", "restore": "Rest<PERSON>"}, "deleteConfirmTitle": "Confirm Delete Agent Type", "deleteConfirmMessage": "Are you sure you want to delete this agent type? This action will move the agent type to trash and can be restored.", "deleteSuccess": "Delete Successful", "deleteSuccessMessage": "Agent type has been deleted successfully", "deleteError": "An error occurred while deleting agent type", "selectTypeToDelete": "Select agent type to delete", "deleteWithMigration": "Delete with Migration", "deleteWithMigrationDescription": "Delete agent type and migrate all agents of this type to the newly selected type.", "newTypeAgent": "New agent type", "selectNewType": "Select new agent type", "selectNewTypeDescription": "Select new agent type to migrate current agents.", "noAvailableTypes": "No other agent types available for migration. You can only delete without migration.", "deleteOnly": "Delete Only"}, "card": {"supervisor": "Supervisor", "active": "Active", "inactive": "Inactive", "activate": "Activate", "deactivate": "Deactivate", "edit": "Edit", "delete": "Delete", "restore": "Rest<PERSON>", "memories": "Memories", "confirmDelete": "Confirm Delete Agent", "deleteMessage": "Are you sure you want to delete this Agent? This action cannot be undone.", "deleteSuccess": "Agent deleted successfully", "deleteError": "An error occurred while deleting the Agent", "updateSuccess": "Agent updated successfully", "updateError": "An error occurred while updating the Agent", "setSupervisor": "Set as Supervisor", "removeSupervisor": "Remove Supervisor", "setSupervisorSuccess": "Successfully set as supervisor", "removeSupervisorSuccess": "Successfully removed supervisor privileges", "supervisorError": "An error occurred while changing supervisor privileges", "restoreSuccess": "Agent restored successfully", "restoreError": "An error occurred while restoring the agent"}, "trash": {"noAgents": "No agents in trash", "noAgentsDescription": "Trash is empty. Deleted agents will appear here."}, "edit": {"notFound": "Agent not found"}, "pagination": {"itemsPerPage": "Items per page", "showingItems": "Showing {from} - {to} of {total} items", "page": "Page", "of": "of", "previous": "Previous", "next": "Next"}, "form": {"basicInfo": "Basic Information", "name": "Agent Name", "namePlaceholder": "Enter agent name", "nameCode": "Identifier Code", "nameCodePlaceholder": "Enter identifier code", "instruction": "Instructions", "instructionPlaceholder": "Enter instructions for agent", "description": "Description", "descriptionPlaceholder": "Enter agent description", "avatar": "Avatar", "avatarUpload": "Upload avatar", "avatarHelp": "Supported formats: JPG, PNG (single image only)", "modelConfig": "Model Configuration", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "provider": "Provider Type", "resources": "Resources", "model": "Model", "selectModel": "Select model", "vectorStore": "Vector Store", "selectVectorStore": "Select vector store", "mcpSystems": "MCP Systems", "isSupervisor": "Is Supervisor", "create": "Create Agent", "creating": "Creating Agent...", "createSuccess": "Agent created successfully", "createError": "An error occurred while creating the Agent", "uploadingAvatar": "Uploading avatar...", "uploadAvatarSuccess": "Avatar uploaded successfully", "uploadAvatarError": "An error occurred while uploading avatar", "error": "Error", "errorMessage": "An error occurred while creating agent", "updateError": "An error occurred while updating agent"}, "upload": {"success": "Upload successful", "successMessage": "All files uploaded successfully", "error": "Upload error", "errorMessage": "An error occurred while uploading files"}, "validation": {"nameRequired": "Agent name is required", "nameCodeRequired": "Identifier code is required", "nameCodeFormat": "Identifier code can only contain lowercase letters, numbers, underscores and hyphens", "instructionRequired": "Instructions are required", "descriptionRequired": "Description is required", "modelRequired": "Model is required", "modelIdInvalid": "Model ID must be a valid UUID"}, "mcp": {"config": {"title": "MCP Systems", "description": "Manage MCP Systems for this Agent Type", "addMCPSystem": "Add MCP System", "noMCPSystems": "No MCP Systems yet", "removeMCPSystem": "Remove MCP System", "mcpSystemName": "MCP System Name", "mcpSystemDescription": "MCP System Description"}, "slideIn": {"title": "Select MCP Systems", "save": "Save", "cancel": "Cancel", "noChangesToSave": "No changes to save", "selectAtLeastOne": "Select at least one MCP System", "search": "Search MCP Systems...", "noResults": "No MCP Systems found", "selectAll": "Select All", "deselectAll": "Deselect All", "selected": "Selected {{count}} MCP System", "hasChanges": "Has changes", "addMCPsToListSuccess": "MCP Systems added to list successfully", "updateMCPsSuccess": "MCP Systems updated successfully", "updateMCPsError": "Error occurred while updating MCP Systems", "columns": {"nameServer": "Server Name", "description": "Description", "updatedAt": "Updated"}}}, "memories": {"title": "Saved Memories", "addNew": "Add new memory", "addNewMemory": "Add New Memory", "editMemory": "Edit Memory", "titleLabel": "Title", "titlePlaceholder": "Enter memory title", "contentLabel": "Content", "contentPlaceholder": "Enter memory content", "reasonLabel": "Reason", "reasonPlaceholder": "Enter reason for saving this memory", "edit": "Edit", "delete": "Delete", "empty": "No memories saved yet"}}