import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Icon,
  Card,
  Input,
  Textarea,
  Loading,
  IconCard,
  Tooltip,
  Divider,
} from '@/shared/components/common';
import { apiClient } from '@/shared/api/axios';

interface Memory {
  id: string;
  structuredContent: {
    title: string;
    reason: string;
    content: string;
  };
  createdAt: string;
}

interface MemoriesResponse {
  items: Memory[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
    hasItems: boolean;
  };
}

interface AdminMemoriesManagementFormProps {
  entityType: 'agent' | 'agent-strategy' | 'agent-templates' | 'type-agent';
  entityId: string;
  onCancel: () => void;
  onSuccess?: () => void;
}

interface EditMemoryData {
  structuredContent: {
    title: string;
    content: string;
    reason: string;
  };
}

const AdminMemoriesManagementForm: React.FC<AdminMemoriesManagementFormProps> = ({
  entityType,
  entityId,
  onCancel,
  onSuccess,
}) => {
  const { t } = useTranslation(['admin', 'common']);

  const [memories, setMemories] = useState<Memory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [editingMemory, setEditingMemory] = useState<Memory | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    reason: '',
  });

  // Get API endpoint based on entity type
  const getApiEndpoint = useCallback((action: 'list' | 'create' | 'update' | 'delete', memoryId?: string) => {
    const baseUrl = `/admin/agent/${entityId}/memories`;
    switch (action) {
      case 'list':
      case 'create':
        return baseUrl;
      case 'update':
      case 'delete':
        return `${baseUrl}/${memoryId}`;
      default:
        return baseUrl;
    }
  }, [entityId]);

  // Load memories
  const loadMemories = useCallback(async () => {
    if (!entityId) return;

    setIsLoading(true);
    try {
      const response = await apiClient.get<MemoriesResponse>(getApiEndpoint('list'), {
        params: {
          page: 1,
          limit: 40,
          sortBy: 'createdAt',
          sortDirection: 'DESC',
        },
        tokenType: 'admin',
      });

      if (response.result?.items) {
        setMemories(response.result.items);
      }
    } catch (err) {
      console.error('Error loading memories:', err);
    } finally {
      setIsLoading(false);
    }
  }, [entityId, getApiEndpoint]);

  // Delete memory
  const handleDeleteMemory = async (memoryId: string) => {
    try {
      await apiClient.delete(getApiEndpoint('delete', memoryId), {
        tokenType: 'admin',
      });

      console.log('Memory deleted successfully');
      loadMemories(); // Reload list
      if (onSuccess) onSuccess();
    } catch (err) {
      console.error('Error deleting memory:', err);
    }
  };

  // Edit memory
  const handleEditMemory = (memory: Memory) => {
    setEditingMemory(memory);
    setFormData({
      title: memory.structuredContent.title,
      content: memory.structuredContent.content,
      reason: memory.structuredContent.reason,
    });
  };

  // Update memory
  const handleUpdateMemory = async () => {
    if (!editingMemory) return;

    try {
      const updateData: EditMemoryData = {
        structuredContent: {
          title: formData.title,
          content: formData.content,
          reason: formData.reason,
        },
      };

      await apiClient.put(getApiEndpoint('update', editingMemory.id), updateData, {
        tokenType: 'admin',
      });

      console.log('Memory updated successfully');
      setEditingMemory(null);
      setFormData({ title: '', content: '', reason: '' });
      loadMemories(); // Reload list
      if (onSuccess) onSuccess();
    } catch (err) {
      console.error('Error updating memory:', err);
    }
  };

  // Add new memory
  const handleAddMemory = async () => {
    try {
      const addData: EditMemoryData = {
        structuredContent: {
          title: formData.title,
          content: formData.content,
          reason: formData.reason,
        },
      };

      await apiClient.post(getApiEndpoint('create'), addData, {
        tokenType: 'admin',
      });

      console.log('Memory added successfully');
      setIsAddingNew(false);
      setFormData({ title: '', content: '', reason: '' });
      loadMemories(); // Reload list
      if (onSuccess) onSuccess();
    } catch (err) {
      console.error('Error adding memory:', err);
    }
  };

  // Cancel edit/add
  const handleCancel = () => {
    setEditingMemory(null);
    setIsAddingNew(false);
    setFormData({ title: '', content: '', reason: '' });
  };

  // Load memories when component mounts
  useEffect(() => {
    if (entityId) {
      loadMemories();
    }
  }, [entityId, entityType, loadMemories]);

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-border">
        <div className="flex items-center space-x-3">

          <Typography variant="h5" className="font-semibold">
            {t('admin:memories.title', 'Bộ nhớ đã lưu')}
          </Typography>
        </div>
        <div className=' flex items-center space-x-2'>

          <Tooltip content={t('admin:memories.addNew', 'Thêm memory mới')} position="top">
            <IconCard
              icon="plus"
              variant="primary"
              size="md"
              onClick={() => setIsAddingNew(true)}
            />
          </Tooltip>
          <IconCard
            icon="x"
            variant="primary"
            size="md"
            title={t('common:cancel', 'Hủy')}
            onClick={onCancel}
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-4">
          {/* Header with Add button */}
          <div className="flex justify-between items-center">


          </div>

          {/* Loading state */}
          {isLoading && (
            <div className="flex justify-center py-8">
              <Loading size="lg" />
            </div>
          )}

          {/* Add new memory form */}
          {isAddingNew && (
            <Card className="p-4 border-1 border-primary-200">
              <div className="space-y-4">
                <Typography variant="subtitle2" className="font-medium">
                  {t('admin:memories.addNewMemory', 'Thêm Memory Mới')}
                </Typography>

                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:memories.titleLabel', 'Tiêu đề')}
                  </label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder={t('admin:memories.titlePlaceholder', 'Nhập tiêu đề memory')}
                    fullWidth
                  />
                </div>

                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:memories.contentLabel', 'Nội dung')}
                  </label>
                  <Textarea
                    value={formData.content}
                    onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                    placeholder={t('admin:memories.contentPlaceholder', 'Nhập nội dung memory')}
                    rows={3}
                    fullWidth
                  />
                </div>

                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:memories.reasonLabel', 'Lý do')}
                  </label>
                  <Textarea
                    value={formData.reason}
                    onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                    placeholder={t('admin:memories.reasonPlaceholder', 'Nhập lý do lưu memory này')}
                    rows={2}
                    fullWidth
                  />
                </div>

                <div className="flex justify-end space-x-2">
                  <IconCard
                    icon="x"
                    variant="secondary"
                    size="md"
                    title={t('common:cancel', 'Hủy')}
                    onClick={handleCancel}
                  />
                  <IconCard
                    icon="save"
                    variant="primary"
                    size="md"
                    title={t('common:save', 'Lưu')}
                    onClick={handleAddMemory}
                    disabled={!formData.title || !formData.content}
                  />

                </div>
              </div>
            </Card>
          )}

          {/* Edit memory form */}
          {editingMemory && (
            <Card className="p-4 ">
              <div className="space-y-4">
                <Typography variant="subtitle2" className="font-medium">
                  {t('admin:memories.editMemory', 'Chỉnh sửa Memory')}
                </Typography>

                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:memories.titleLabel', 'Tiêu đề')}
                  </label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder={t('admin:memories.titlePlaceholder', 'Nhập tiêu đề memory')}
                    fullWidth
                  />
                </div>

                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:memories.contentLabel', 'Nội dung')}
                  </label>
                  <Textarea
                    value={formData.content}
                    onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                    placeholder={t('admin:memories.contentPlaceholder', 'Nhập nội dung memory')}
                    rows={3}
                    fullWidth
                  />
                </div>

                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:memories.reasonLabel', 'Lý do')}
                  </label>
                  <Textarea
                    value={formData.reason}
                    onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                    placeholder={t('admin:memories.reasonPlaceholder', 'Nhập lý do lưu memory này')}
                    rows={2}
                    fullWidth
                  />
                </div>

                <div className="flex justify-end space-x-2">
                  <IconCard
                    icon="save"
                    variant="primary"
                    size="md"
                    title={t('common:save', 'Lưu')}
                    onClick={handleUpdateMemory}
                    disabled={!formData.title || !formData.content}
                  />
                  <IconCard
                    icon="x"
                    variant="secondary"
                    size="md"
                    title={t('common:cancel', 'Hủy')}
                    onClick={handleCancel}
                  />

                </div>

              </div>
            </Card>
          )}

          {/* Memories list */}
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {memories.map((memory) => (
              <Card key={memory.id} className="p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <Typography variant="subtitle2" className="font-medium mb-2">
                      {memory.structuredContent.title}
                    </Typography>
                    <Typography variant="body2" className="text-muted mb-2">
                      {memory.structuredContent.content}
                    </Typography>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <Tooltip content={t('admin:memories.edit', 'Chỉnh sửa')} position="top">
                      <IconCard
                        icon="edit"
                        variant="default"
                        size="sm"
                        onClick={() => handleEditMemory(memory)}
                        className="text-blue-500 hover:text-blue-600"
                      />
                    </Tooltip>

                    <Tooltip content={t('admin:memories.delete', 'Xóa')} position="top">
                      <IconCard
                        icon="trash"
                        variant="default"
                        size="sm"
                        onClick={() => handleDeleteMemory(memory.id)}
                        className="text-red-500 hover:text-red-600"
                      />
                    </Tooltip>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Empty state */}
          {!isLoading && memories.length === 0 && !isAddingNew && (
            <div className="  py-1">
              <div className="flex justify-center items-center">
                <Icon name="brain" size="xl" className="text-gray-400" />
              </div>
              <Typography variant="body1" className="text-muted mt-2 text-center">
                {t('admin:memories.empty', 'Chưa có memory nào được lưu')}
              </Typography>
            </div>
          )}

        </div>
      </div>
      <Divider />
    </div>
  );
};

export default AdminMemoriesManagementForm;
