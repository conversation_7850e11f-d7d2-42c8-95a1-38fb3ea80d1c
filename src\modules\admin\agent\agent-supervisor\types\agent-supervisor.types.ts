/**
 * Enum cho trạng thái agent supervisor
 */
export enum AgentSupervisorStatusEnum {
  true = 'true',
  false = 'false',
}

/**
 * Enum cho các trường sắp xếp của agent supervisor
 */
export enum AgentSupervisorSortBy {
  ID = 'id',
  NAME = 'name',
  ACTIVE = 'active',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho cấu hình model AI
 */
export interface ModelConfig {
  /** Temperature cho model (0-2) */
  temperature?: number;
  /** Top P (0-1) */
  top_p?: number;
  /** Top K */
  top_k?: number;
  /** Max tokens */
  max_tokens?: number;
}

/**
 * Interface cho agent supervisor trong danh sách
 */
export interface AgentSupervisorListItem {
  /** ID của agent supervisor */
  id: string;
  /** Tên hiển thị của agent supervisor */
  name: string;
  /** URL avatar của agent supervisor */
  avatar: string | null;
  /** Tên model sử dụng */
  model: string;
  /** Trạng thái active */
  active: boolean;
  /** Provider của model */
  provider: string;
}

/**
 * Interface cho Model trong agent supervisor detail
 */
export interface AgentSupervisorModelDetail {
  /** ID của model */
  id: string;
  /** Model ID từ provider */
  modelId: string;
  /** Provider của model */
  provider: string;
  /** Pattern tên model */
  modelNamePattern: string;
  /** Trạng thái active */
  active: boolean;
}

/**
 * Interface cho MCP System trong agent supervisor detail
 */
export interface AgentSupervisorMCPDetail {
  /** ID của MCP system */
  id: string;
  /** Tên server */
  nameServer: string;
  /** Mô tả */
  description: string;
  /** Cấu hình */
  config: {
    url: string;
    transport: string;
    automaticSSEFallback: boolean;
  };
}

/**
 * Interface cho thông tin chi tiết agent supervisor
 */
export interface AgentSupervisorDetail {
  /** ID của agent supervisor */
  id: string;
  /** Tên hiển thị của agent supervisor */
  name: string;
  /** Name code */
  nameCode?: string;
  /** URL avatar của agent supervisor */
  avatar: string | null;
  /** Cấu hình model AI */
  modelConfig: ModelConfig;
  /** Hướng dẫn hoặc system prompt cho agent supervisor */
  instruction: string | null;
  /** Mô tả của agent supervisor */
  description?: string;
  /** Thông tin model chi tiết */
  model: AgentSupervisorModelDetail;
  /** Danh sách MCP systems */
  mcp?: AgentSupervisorMCPDetail[];
  /** Danh sách ID của files */
  fileIds?: string[];
  /** Danh sách files chi tiết */
  files?: Array<{
    id: string;
    name: string;
  }>;
  /** Thời gian tạo */
  createdAt?: number;
  /** Thời gian cập nhật */
  updatedAt?: number;
}

/**
 * Interface cho agent supervisor đã xóa trong trash
 */
export interface AgentSupervisorTrashItem {
  /** ID của agent supervisor */
  id: string;
  /** Tên hiển thị của agent supervisor */
  name: string;
  /** URL avatar của agent supervisor */
  avatar?: string | null;
  /** Tên model sử dụng */
  model: string;
  /** Trạng thái active */
  active: boolean;
  /** Provider của model */
  provider: string;
  /** Thời gian xóa */
  deletedAt?: number;
}

/**
 * Interface cho tham số tạo agent supervisor
 */
export interface CreateAgentSupervisorParams {
  /** Tên hiển thị của agent supervisor */
  name: string;
  /** MIME type của avatar */
  avatarMimeType?: string;
  /** Cấu hình model AI */
  modelConfig: ModelConfig;
  /** Hướng dẫn hoặc system prompt cho agent supervisor */
  instruction?: string | null;
  /** Mô tả của agent supervisor */
  description?: string;
  /** ID của model */
  modelId?: string;
  /** Danh sách ID của MCP systems */
  mcpId?: string[];
  /** Danh sách ID của files */
  fileIds?: string[];
}

/**
 * Interface cho tham số cập nhật agent supervisor
 */
export interface UpdateAgentSupervisorParams {
  /** Tên hiển thị của agent supervisor */
  name?: string;
  /** MIME type của avatar */
  avatarMimeType?: string;
  /** Cấu hình model AI */
  modelConfig?: ModelConfig;
  /** Hướng dẫn hoặc system prompt cho agent supervisor */
  instruction?: string | null;
  /** Mô tả của agent supervisor */
  description?: string;
  /** ID của model */
  modelId?: string;
  /** Danh sách ID của MCP systems */
  mcpId?: string[];
  /** Danh sách ID của files */
  fileIds?: string[];
}

/**
 * Interface cho tham số query agent supervisor
 */
export interface AgentSupervisorQueryParams {
  /** Số trang */
  page?: number;
  /** Số lượng item trên mỗi trang */
  limit?: number;
  /** Từ khóa tìm kiếm */
  search?: string;
  /** Sắp xếp theo trường */
  sortBy?: AgentSupervisorSortBy;
  /** Hướng sắp xếp */
  sortDirection?: SortDirection;
}

/**
 * Interface cho response tạo agent supervisor
 */
export interface CreateAgentSupervisorResponse {
  /** ID của agent supervisor đã tạo */
  id: string;
  /** URL tải lên avatar (nếu có) */
  avatarUrlUpload?: string;
}

/**
 * Interface cho response cập nhật agent supervisor
 */
export interface UpdateAgentSupervisorResponse {
  /** URL tải lên avatar (nếu có) */
  avatarUrlUpload?: string;
}

/**
 * Interface cho response khôi phục agent supervisor
 */
export interface RestoreAgentSupervisorParams {
  /** Danh sách ID cần khôi phục */
  ids: string[];
}

/**
 * Interface cho Knowledge File
 */
export interface KnowledgeFile {
  /** ID của file */
  id: string;
  /** Tên file */
  name: string;
  /** Phần mở rộng file */
  extension: string;
  /** Kích thước file (bytes) */
  storage: number;
  /** Loại owner */
  ownerType: string;
  /** Có được gán vào vector store không */
  isAssignedToVectorStore: boolean;
  /** URL download */
  downloadURL: string;
  /** Thời gian tạo */
  createdAt: number;
  /** Thời gian cập nhật */
  updatedAt: number;
}

/**
 * Interface cho query params của Knowledge Files
 */
export interface KnowledgeFileQueryParams {
  /** Số trang */
  page?: number;
  /** Số lượng item trên mỗi trang */
  limit?: number;
  /** Từ khóa tìm kiếm */
  search?: string;
  /** Sắp xếp theo trường */
  sortBy?: string;
}
