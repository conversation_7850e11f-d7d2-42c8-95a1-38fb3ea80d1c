import React, { useState } from 'react';
import { Card, IconCard, Tooltip, Chip, Modal, Button, Typography } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { AgentSupervisorListItem } from '../agent-supervisor/types/agent-supervisor.types';
import {
  useDeleteAgentSupervisor,
  useToggleActiveAgentSupervisor,
  useRestoreAgentSupervisors
} from '../agent-supervisor/hooks/use-agent-supervisor';

interface AdminAgentSupervisorCardProps {
  agent: AgentSupervisorListItem;
  /** C<PERSON> phải là trang trash không */
  isTrashPage?: boolean;
  /** Callback khi click edit */
  onEditAgent?: (agentId: string) => void;
  /** Callback khi cần refresh data */
  onRefresh?: () => void;
}

/**
 * Component hiển thị thông tin của một Agent Supervisor
 */
const AdminAgentSupervisorCard: React.FC<AdminAgentSupervisorCardProps> = ({
  agent,
  isTrashPage = false,
  onEditAgent,
  onRefresh
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { success, error } = useSmartNotification();

  // Hooks cho API calls
  const deleteAgentMutation = useDeleteAgentSupervisor();
  const toggleActiveMutation = useToggleActiveAgentSupervisor();
  const restoreAgentMutation = useRestoreAgentSupervisors();

  const isCurrentActive = agent.active === true;

  const handleEditAgent = () => {
    if (onEditAgent) {
      onEditAgent(agent.id);
    } else {
      // Fallback: Navigate đến trang edit agent
      navigate(`/admin/agent/supervisor/${agent.id}/edit`);
    }
  };

  const handleToggleActive = async () => {
    try {
      await toggleActiveMutation.mutateAsync(agent.id);
      success({ message: t('admin:agent.supervisor.toggleActiveSuccess', 'Thay đổi trạng thái thành công') });
      if (onRefresh) {
        onRefresh();
      }
    } catch (err) {
      console.error('Error toggling agent supervisor active status:', err);
      error({ message: err instanceof Error ? err.message : t('admin:agent.supervisor.toggleActiveError', 'Có lỗi xảy ra khi thay đổi trạng thái') });
    }
  };

  const handleDeleteAgent = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id);
      success({ message: t('admin:agent.supervisor.deleteSuccess', 'Xóa Agent Supervisor thành công') });
      setShowDeleteModal(false);
      if (onRefresh) {
        onRefresh();
      }
    } catch (err) {
      console.error('Error deleting agent supervisor:', err);
      error({ message: err instanceof Error ? err.message : t('admin:agent.supervisor.deleteError', 'Có lỗi xảy ra khi xóa Agent Supervisor') });
    }
  };

  const handleRestoreAgent = async () => {
    try {
      await restoreAgentMutation.mutateAsync({ ids: [agent.id] });
      success({ message: t('admin:agent.supervisor.restoreSuccess', 'Khôi phục Agent Supervisor thành công') });
      if (onRefresh) {
        onRefresh();
      }
    } catch (err) {
      console.error('Error restoring agent supervisor:', err);
      error({ message: err instanceof Error ? err.message : t('admin:agent.supervisor.restoreError', 'Có lỗi xảy ra khi khôi phục Agent Supervisor') });
    }
  };

  const handleConfirmDelete = () => {
    setShowDeleteModal(true);
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
  };

  // Xác định variant cho model chip dựa trên nhà cung cấp
  const getModelVariant = (
    model: string
  ): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
    const modelLower = model.toLowerCase();

    // OpenAI models
    if (modelLower.includes('gpt')) return 'danger';

    // Anthropic models
    if (modelLower.includes('claude')) return 'success';

    // Google models
    if (modelLower.includes('gemini')) return 'info';

    // DeepSeek models
    if (modelLower.includes('deepseek')) return 'warning';

    // Mistral models
    if (modelLower.includes('mistral')) return 'primary';

    // Llama models
    if (modelLower.includes('llama')) return 'info';

    // Default for other models
    return 'primary';
  };

  return (
    <>
      <Card
        className="h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
        variant="elevated"
      >
        <div className="p-4">
          <div className="flex flex-col space-y-4">
            {/* Hàng 1: Avatar, tên, và model */}
            <div className="flex items-center gap-3 overflow-hidden">
              {/* Avatar */}
              <div className="flex items-center gap-2">
                <div className="relative w-16 h-16 sm:w-20 sm:h-20 flex-shrink-0">
                  <div className="w-full h-full relative">
                    <img
                      src="/assets/images/frame-level-agents.png"
                      alt="Level frame"
                      className="absolute inset-0 w-full h-full object-contain z-10"
                    />
                    <div className="absolute inset-0 flex items-center justify-center z-0">
                      <img
                        src={agent.avatar || '/assets/images/default-avatar.png'}
                        alt={agent.name}
                        className="w-[75%] h-[75%] rounded-full object-cover"
                      />
                    </div>
                    {/* Chỉ báo trạng thái active */}
                    <div
                      className={`absolute bottom-1 right-1 w-3 h-3 rounded-full z-20 ${
                        agent.active ? 'bg-green-500 dark:bg-green-400' : 'bg-gray-300 dark:bg-gray-600'
                      }`}
                    />
                    {/* Supervisor badge */}
                   
                  </div>
                </div>
              </div>

              {/* Thông tin agent: tên và model */}
              <div className="flex flex-col min-w-0 flex-grow">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                  <div className="min-w-0">
                    <Typography variant="h5" className="font-semibold text-gray-900 dark:text-white truncate">
                      {agent.name}
                    </Typography>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {agent.provider || t('common.unknown', 'Unknown')}
                    </div>
                  </div>
                  <div className="flex-shrink-0 mt-1 sm:mt-0">
                    <Chip
                      variant={getModelVariant(agent.model)}
                      size="sm"
                      className="font-normal max-w-full truncate"
                    >
                      {agent.model}
                    </Chip>
                  </div>
                </div>
              </div>
            </div>

            {/* Hàng 2: Các nút chức năng */}
            <div className="flex justify-between">
              <div>
                {/* Placeholder for future features */}
              </div>

              <div className="flex space-x-6">
                {isTrashPage ? (
                  // Chỉ hiển thị nút restore trong trang trash
                  <Tooltip content={t('admin:agent.supervisor.card.restore', 'Khôi phục')} position="top">
                    <IconCard
                      icon="chevron-up"
                      variant="primary"
                      size="md"
                      onClick={handleRestoreAgent}
                      className="text-green-500 hover:text-green-600"
                      disabled={restoreAgentMutation.isPending}
                    />
                  </Tooltip>
                ) : (
                  // Hiển thị các nút thông thường trong trang chính
                  <>
                    <Tooltip
                      content={isCurrentActive ? t('admin:agent.supervisor.card.deactivate', 'Tắt') : t('admin:agent.supervisor.card.activate', 'Bật')}
                      position="top"
                    >
                      <IconCard
                        icon="power"
                        variant={isCurrentActive ? 'primary' : 'default'}
                        size="md"
                        onClick={handleToggleActive}
                        className={isCurrentActive ? 'text-green-500 hover:text-green-600' : 'text-gray-400 hover:text-gray-600'}
                        disabled={toggleActiveMutation.isPending}
                      />
                    </Tooltip>
                    <Tooltip content={t('admin:agent.supervisor.card.edit', 'Chỉnh sửa')} position="top">
                      <IconCard icon="edit" variant="default" size="md" onClick={handleEditAgent} />
                    </Tooltip>
                    <Tooltip content={t('admin:agent.supervisor.card.delete', 'Xóa')} position="top">
                      <IconCard
                        icon="trash"
                        variant="default"
                        size="md"
                        onClick={handleConfirmDelete}
                        className="text-red-500 hover:text-red-600"
                        disabled={deleteAgentMutation.isPending}
                      />
                    </Tooltip>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        title={t('admin:agent.supervisor.card.confirmDelete', 'Confirm Delete Agent Supervisor')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button
              variant="primary"
              onClick={handleDeleteAgent}
              isLoading={deleteAgentMutation.isPending}
              disabled={deleteAgentMutation.isPending}
            >
              {t('common:delete', 'Delete')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography className="mb-4">
            {t(
              'admin:agent.supervisor.card.deleteMessage',
              'Are you sure you want to delete this Agent Supervisor? This action cannot be undone.'
            )}
          </Typography>
        </div>
      </Modal>
    </>
  );
};

export default AdminAgentSupervisorCard;
