import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination } from '@/shared/components/common';
import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { useDeletedAgentSupervisors } from '../agent-supervisor/hooks/use-agent-supervisor';
import { AgentSupervisorTrashItem, AgentSupervisorQueryParams } from '../agent-supervisor/types/agent-supervisor.types';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import AdminAgentSupervisorCard from '../components/AdminAgentSupervisorCard';

/**
 * Trang hiển thị danh sách Agent Supervisors đã xóa
 */
const AgentSupervisorDeletePage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');

  // Query params
  const queryParams: AgentSupervisorQueryParams = {
    page,
    limit,
    search: search || undefined,
  };

  // Lấy danh sách agent supervisors đã xóa
  const { data: agentsResponse, isLoading, error, refetch } = useDeletedAgentSupervisors(queryParams);

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleBackToMain = () => {
    navigate('/admin/agent/supervisor');
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };



  // Transform dữ liệu từ API thành format phù hợp với component
  const agents = useMemo(() => {
    if (!(agentsResponse as any)?.items) {
      return [];
    }

    return (agentsResponse as any).items.map((apiAgent: AgentSupervisorTrashItem) => ({
      id: apiAgent.id,
      name: apiAgent.name,
      avatar: apiAgent.avatar,
      model: apiAgent.model,
      active: apiAgent.active,
      provider: apiAgent.provider,
    }));
  }, [agentsResponse]);

  const totalItems = (agentsResponse as any)?.meta?.totalItems || 0;

  // Additional icons cho MenuIconBar
  const additionalIcons = [
    {
      icon: 'arrow-left',
      tooltip: t('admin:agent.supervisor.backToMain', 'Back to Main List'),
      onClick: handleBackToMain,
      variant: 'default' as const,
    },
    {
      icon: 'refresh',
      tooltip: t('common:refresh', 'Refresh'),
      onClick: () => refetch(),
      variant: 'default' as const,
    },
  ];

  // Xử lý loading state
  if (isLoading) {
    return (
      <div className="p-6">
        <MenuIconBar
          onSearch={handleSearch}
          additionalIcons={additionalIcons}
        />
        <Loading />
      </div>
    );
  }

  // Xử lý error state
  if (error) {
    return (
      <div className="p-6">
        <MenuIconBar
          onSearch={handleSearch}
          additionalIcons={additionalIcons}
        />
        <EmptyState
          title={t('admin:agent.supervisor.trash.loadError', 'Unable to load deleted Agent Supervisor list')}
          actions={[
            <Button key="refresh" onClick={() => refetch()}>
              {t('common:refresh', 'Refresh')}
            </Button>
          ]}
        />
      </div>
    );
  }

  // Xử lý empty state
  if (!isLoading && agents.length === 0 && !search) {
    return (
      <div className="p-6">
        <MenuIconBar
          onSearch={handleSearch}
          additionalIcons={additionalIcons}
        />
        <EmptyState
          title={t('admin:agent.supervisor.trash.noSupervisors', 'No Agent Supervisors in trash')}
          description={t('admin:agent.supervisor.trash.noSupervisorsDescription', 'Trash is empty. Deleted Agent Supervisors will appear here.')}
          actions={[
            <Button key="back" onClick={handleBackToMain}>
              {t('admin:agent.supervisor.backToMain', 'Back to Main List')}
            </Button>
          ]}
        />
      </div>
    );
  }

  // Xử lý no search results
  if (!isLoading && agents.length === 0 && search) {
    return (
      <div className="p-6">
        <MenuIconBar
          onSearch={handleSearch}
          additionalIcons={additionalIcons}
        />
        <EmptyState
          title={t('admin:agent.supervisor.trash.noSearchResults', 'No Agent Supervisors found matching criteria')}
          actions={[
            <Button key="clear" onClick={() => setSearch('')}>
              {t('common:clearSearch', 'Clear Search')}
            </Button>
          ]}
        />
      </div>
    );
  }

  return (
    <div className="p-1">
      <MenuIconBar
        onSearch={handleSearch}
        additionalIcons={additionalIcons}
      />

      {/* Agent Grid */}
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
        gap={{ xs: 4, md: 5, lg: 6 }}
      >
        {agents.map((agent: any) => (
          <div key={agent.id} className="h-full">
            <AdminAgentSupervisorCard
              agent={agent}
              isTrashPage={true}
              onRefresh={refetch}
            />
          </div>
        ))}
      </ResponsiveGrid>

      {/* Pagination */}
      {totalItems > limit && (
        <div className="mt-8 flex justify-end">
          <Pagination
            currentPage={page}
            totalItems={totalItems}
            itemsPerPage={limit}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleLimitChange}
            itemsPerPageOptions={[10, 20, 50, 100]}
            showItemsPerPageSelector={true}
            showPageInfo={true}
            variant="compact"
            borderless={true}
          />
        </div>
      )}
    </div>
  );
};

export default AgentSupervisorDeletePage;
