import React, { useState, useRef, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Icon,
  Slider,
  IconCard,
  ResponsiveGrid,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { z } from 'zod';
import { apiClient } from '@/shared/api/axios';
import { useAdminAgentNotification } from '../hooks/useAdminAgentNotification';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { getProviderIcon } from '@/modules/admin/integration/provider-model/types';

// Import the correct types from service
import {
  StrategyContent,
  StrategyExample,
  CreateAgentStrategyResponse,
} from '../agent-strategy/types/agent-strategy.types';
import { useAdminSystemModels, AdminSystemModel } from '../hooks/useAdminSystemModels';

// Types - sử dụng interface từ types file

// SystemModel interface đã được import từ useAdminSystemModels hook

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false,
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-foreground">{name}</div>
      </div>
    </Card>
  );
};

interface AddAgentStrategyFormProps {
  onCancel: () => void;
  onSuccess?: () => void;
}

// Danh sách providers - di chuyển ra ngoài component để tránh re-render
const PROVIDERS = [
  { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
  { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
  { type: TypeProviderEnum.GEMINI, name: 'Gemini' },
  { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
  { type: TypeProviderEnum.XAI, name: 'XAI' },
];

// Schema validation
const createAgentStrategySchema = (t: any) =>
  z.object({
    name: z
      .string()
      .min(1, t('admin:agent.strategy.validation.nameRequired', 'Tên chiến lược là bắt buộc'))
      .trim(),
    instruction: z
      .string()
      .min(1, t('admin:agent.strategy.validation.instructionRequired', 'Hướng dẫn là bắt buộc'))
      .trim(),
    modelId: z
      .string()
      .min(1, t('admin:agent.strategy.validation.modelRequired', 'Model là bắt buộc'))
      .uuid(
        t(
          'admin:agent.strategy.validation.modelRequired',
          'Model ID phải là UUID hợp lệ'
        )
      ),
  });

const AddAgentStrategyForm: React.FC<AddAgentStrategyFormProps> = ({ onCancel, onSuccess }) => {
  const { t } = useTranslation(['admin', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);
  const { createSuccess, createError, uploadSuccess, uploadError, validationError, processing } =
    useAdminAgentNotification();

  // Avatar upload states
  const [avatarFiles, setAvatarFiles] = useState<FileWithMetadata[]>([]);

  // Model config states
  const [modelConfig, setModelConfig] = useState<Record<string, number>>({
    temperature: 1,
    top_p: 1,
    top_k: 1,
    max_tokens: 1000,
  });

  // Content and example states
  const [contentSteps, setContentSteps] = useState<StrategyContent[]>([
    { stepOrder: 1, content: '' },
  ]);
  const [exampleSteps, setExampleSteps] = useState<StrategyExample[]>([
    { stepOrder: 1, content: '' },
  ]);

  // System models states
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum | null>(null);
  const [selectedModel, setSelectedModel] = useState<AdminSystemModel | null>(null);

  // Fetch system models using hook
  const { data: systemModelsResponse, isLoading: loadingSystemModels } = useAdminSystemModels({
    page: 1,
    limit: 40,
    sortBy: 'systemModels.modelId',
    provider: selectedProvider || TypeProviderEnum.OPENAI,
    enabled: !!selectedProvider, // Chỉ fetch khi đã chọn provider
  });

  // Default form values
  const defaultValues = {
    name: '',
    instruction: '',
    modelId: '',
  };

  // Memoize system models options để tránh re-render liên tục
  const systemModelOptions = useMemo(() => {
    return (
      systemModelsResponse?.items?.map(model => ({
        value: model.id,
        label: model.modelName,
      })) || []
    );
  }, [systemModelsResponse?.items]);

  // Handle provider selection
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    setSelectedProvider(provider);
    setSelectedModel(null);
    // Reset form field khi đổi provider
    if (formRef.current) {
      formRef.current.setValues({ modelId: '' });
    }
    // Reset model config về default
    setModelConfig({
      temperature: 1,
      top_p: 1,
      top_k: 1,
      max_tokens: 1000,
    });
  };

  // Handle model selection
  const handleModelSelect = (modelId: string) => {
    const model = systemModelsResponse?.items?.find(m => m.id === modelId);
    setSelectedModel(model || null);

    if (model) {
      // Tạo modelConfig chỉ với các parameters mà model hỗ trợ
      const newModelConfig: Record<string, number> = {};

      if (model.samplingParameters.includes('temperature')) {
        newModelConfig['temperature'] = 1;
      }
      if (model.samplingParameters.includes('top_p')) {
        newModelConfig['top_p'] = 1;
      }
      if (model.samplingParameters.includes('top_k')) {
        newModelConfig['top_k'] = 1;
      }
      // Luôn thêm max_tokens với giá trị mặc định dựa trên maxTokens của model
      const maxTokensLimit = parseInt(model.maxTokens) || 8000;
      const defaultMaxTokens = Math.min(1000, maxTokensLimit); // Mặc định 1000 hoặc maxTokens nếu nhỏ hơn
      newModelConfig['max_tokens'] = defaultMaxTokens;

      setModelConfig(newModelConfig);

      console.log('🔍 [AddAgentStrategyForm] Model selected:', {
        modelId: model.modelName,
        samplingParameters: model.samplingParameters,
        newModelConfig,
      });
    }
  };

  // Handle model config changes
  const handleModelConfigChange = (key: keyof typeof modelConfig, value: number) => {
    // Validation đặc biệt cho max_tokens
    if (key === 'max_tokens' && selectedModel?.maxTokens) {
      const maxTokensLimit = parseInt(selectedModel.maxTokens);
      if (value > maxTokensLimit) {
        value = maxTokensLimit; // Giới hạn không vượt quá maxTokens của model
      }
    }

    setModelConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle avatar file selection - chỉ cho phép 1 ảnh
  const handleAvatarChange = useCallback((files: FileWithMetadata[]) => {
    // Chỉ lấy file đầu tiên nếu có nhiều file
    if (files.length > 0 && files[0]) {
      setAvatarFiles([files[0]]);
    } else {
      setAvatarFiles([]);
    }
  }, []);

  // Handle content steps
  const addContentStep = () => {
    const newStep: StrategyContent = {
      stepOrder: contentSteps.length + 1,
      content: '',
    };
    setContentSteps([...contentSteps, newStep]);

    // Đồng bộ thêm example step
    const newExampleStep: StrategyExample = {
      stepOrder: exampleSteps.length + 1,
      content: '',
    };
    setExampleSteps([...exampleSteps, newExampleStep]);
  };

  const removeContentStep = (index: number) => {
    if (contentSteps.length > 1) {
      const newSteps = contentSteps.filter((_, i) => i !== index);
      // Reorder step numbers
      const reorderedSteps = newSteps.map((step, i) => ({
        ...step,
        stepOrder: i + 1,
      }));
      setContentSteps(reorderedSteps);

      // Đồng bộ xóa example step
      if (exampleSteps.length > 1) {
        const newExampleSteps = exampleSteps.filter((_, i) => i !== index);
        const reorderedExampleSteps = newExampleSteps.map((step, i) => ({
          ...step,
          stepOrder: i + 1,
        }));
        setExampleSteps(reorderedExampleSteps);
      }
    }
  };

  const updateContentStep = (index: number, content: string) => {
    const newSteps = [...contentSteps];
    if (newSteps[index]) {
      newSteps[index] = {
        ...newSteps[index],
        content,
        stepOrder: newSteps[index].stepOrder || index + 1,
      };
      setContentSteps(newSteps);
    }
  };

  // Handle example steps


  const updateExampleStep = (index: number, content: string) => {
    const newSteps = [...exampleSteps];
    if (newSteps[index]) {
      newSteps[index] = {
        ...newSteps[index],
        content,
        stepOrder: newSteps[index].stepOrder || index + 1,
      };
      setExampleSteps(newSteps);
    }
  };

  // Upload image file to S3 using presigned URL
  const uploadImageFile = async (file: File, presignedUrl: string) => {
    try {
      console.log('🔍 [uploadImageFile] Starting upload to S3...', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        url: presignedUrl.substring(0, 100) + '...', // Log only first 100 chars for security
      });

      const response = await fetch(presignedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type,
        },
        body: file,
      });

      console.log('🔍 [uploadImageFile] Response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries()),
      });

      // S3 PUT thành công thường trả về status 200 hoặc 204
      if (!response.ok) {
        // Không cố gắng đọc response text vì có thể gây CORS error
        throw new Error(`S3 upload failed: ${response.status} ${response.statusText}`);
      }

      console.log('✅ [uploadImageFile] S3 upload successful');
      return true;
    } catch (error) {
      console.error('❌ [uploadImageFile] Upload error:', error);

      // Kiểm tra nếu lỗi là do CORS nhưng upload thực sự thành công
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.warn('⚠️ [uploadImageFile] CORS error detected, but upload might have succeeded');
        // Trong trường hợp này, chúng ta có thể coi như upload thành công
        // vì S3 presigned URL thường không cho phép đọc response từ cross-origin
        return true;
      }

      throw error;
    }
  };

  // Handle form submission
  const handleFormSubmit = async (values: Record<string, unknown>) => {
    console.log('🔍 [AddAgentStrategyForm] Form submitted with values:', values);
    console.log('🔍 [AddAgentStrategyForm] Model config:', modelConfig);
    console.log('🔍 [AddAgentStrategyForm] Avatar files:', avatarFiles);
    console.log('🔍 [AddAgentStrategyForm] Avatar files details:', {
      length: avatarFiles.length,
      firstFile: avatarFiles[0]
        ? {
            name: avatarFiles[0].file.name,
            size: avatarFiles[0].file.size,
            type: avatarFiles[0].file.type,
          }
        : null,
    });
    console.log('🔍 [AddAgentStrategyForm] Content steps:', contentSteps);
    console.log('🔍 [AddAgentStrategyForm] Example steps:', exampleSteps);

    // Provider đã có default value, không cần validate

    // Validate content and examples
    const validContentSteps = contentSteps.filter(step => step.content.trim() !== '');
    const validExampleSteps = exampleSteps.filter(step => step.content.trim() !== '');

    if (validContentSteps.length === 0) {
      validationError(
        t('admin:agent.strategy.validation.contentRequired', 'Nội dung các bước là bắt buộc')
      );
      return;
    }

    if (validExampleSteps.length === 0) {
      validationError(
        t('admin:agent.strategy.validation.exampleRequired', 'Ví dụ mặc định là bắt buộc')
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Tạo modelConfig chỉ với các parameters mà model hỗ trợ
      const filteredModelConfig: Record<string, number> = {};

      if (selectedModel) {
        if (
          selectedModel.samplingParameters.includes('temperature') &&
          modelConfig['temperature'] !== undefined
        ) {
          filteredModelConfig['temperature'] = modelConfig['temperature'];
        }
        if (
          selectedModel.samplingParameters.includes('top_p') &&
          modelConfig['top_p'] !== undefined
        ) {
          filteredModelConfig['top_p'] = modelConfig['top_p'];
        }
        if (
          selectedModel.samplingParameters.includes('top_k') &&
          modelConfig['top_k'] !== undefined
        ) {
          filteredModelConfig['top_k'] = modelConfig['top_k'];
        }
        // Luôn thêm max_tokens nếu có
        if (modelConfig['max_tokens'] !== undefined) {
          const maxTokensLimit = parseInt(selectedModel.maxTokens) || 8000;
          // Đảm bảo max_tokens không vượt quá giới hạn của model
          const validMaxTokens = Math.min(modelConfig['max_tokens'], maxTokensLimit);
          filteredModelConfig['max_tokens'] = validMaxTokens;
        }
      }

      console.log('🔍 [AddAgentStrategyForm] Filtered modelConfig:', {
        selectedModel: selectedModel?.modelName,
        samplingParameters: selectedModel?.samplingParameters,
        originalModelConfig: modelConfig,
        filteredModelConfig,
      });

      // Prepare form data
      const strategyData: Record<string, unknown> = {
        name: values['name'] as string,
        modelConfig: filteredModelConfig,
        instruction: values['instruction'] as string,
        content: validContentSteps,
        exampleDefault: validExampleSteps,
        modelId: values['modelId'] as string,
      };

      // Chỉ thêm avatarMimeType khi có file
      if (avatarFiles.length > 0 && avatarFiles[0]) {
        strategyData['avatarMimeType'] = avatarFiles[0].file.type;
      }

      console.log('🔍 [AddAgentStrategyForm] Submitting strategy data:', strategyData);

      // Submit form directly to API
      const response = await apiClient.post<CreateAgentStrategyResponse>(
        '/admin/agent-strategy',
        strategyData
      );
      console.log('🔍 [AddAgentStrategyForm] Create response:', response);

      // Upload avatar if provided and we have upload URL
      if (avatarFiles.length > 0 && avatarFiles[0] && response.result?.avatarUploadUrl) {
        try {
          console.log(
            '🔍 [AddAgentStrategyForm] Starting avatar upload with URL:',
            response.result.avatarUploadUrl
          );
          processing(t('admin:agent.strategy.form.uploadingAvatar', 'Đang tải lên avatar...'));

          await uploadImageFile(avatarFiles[0].file, response.result.avatarUploadUrl);

          console.log('✅ [AddAgentStrategyForm] Avatar upload completed successfully');
          uploadSuccess('Avatar');
        } catch (uploadErr) {
          console.error('❌ [AddAgentStrategyForm] Avatar upload failed:', uploadErr);
          uploadError(uploadErr instanceof Error ? uploadErr.message : undefined);
        }
      } else {
        console.log('🔍 [AddAgentStrategyForm] No avatar upload needed:', {
          hasAvatarFiles: avatarFiles.length > 0,
          hasFile: !!avatarFiles[0],
          hasUploadUrl: !!response.result?.avatarUploadUrl,
          responseResult: response.result,
        });
      }

      // Show success notification
      createSuccess(t('admin:agent.strategy.pageTitle', 'Chiến lược Agent'));

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error('Error creating strategy:', err);
      createError(
        t('admin:agent.strategy.pageTitle', 'Chiến lược Agent'),
        err instanceof Error ? err.message : undefined
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="">
      <div className="p-2">
        <div className="flex items-center justify-between mb-6">
          <Typography variant="h5" className="font-semibold">
            {t('admin:agent.strategy.addStrategy', 'Thêm Chiến lược mới')}
          </Typography>
          <Button variant="ghost" size="sm" onClick={onCancel} disabled={isSubmitting}>
            <Icon name="x" size="sm" />
          </Button>
        </div>
      </div>

      <Form
        ref={formRef}
        schema={createAgentStrategySchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
        
          <FormGrid columns={1} gap="md">
            <FormItem
              name="name"
              label={t('admin:agent.strategy.form.name', 'Tên Chiến lược')}
              required
            >
              <Input
                fullWidth
                placeholder={t('admin:agent.strategy.form.namePlaceholder', 'Nhập tên chiến lược')}
              />
            </FormItem>

            <FormItem
              name="instruction"
              label={t('admin:agent.strategy.form.instruction', 'Hướng dẫn')}
              required
            >
              <Textarea
                fullWidth
                rows={4}
                placeholder={t(
                  'admin:agent.strategy.form.instructionPlaceholder',
                  'Nhập hướng dẫn cho chiến lược'
                )}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Avatar Upload */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.strategy.form.avatar', 'Avatar')}
          </Typography>

          <MultiFileUpload
            label={t('admin:agent.strategy.form.avatarUpload', 'Tải lên avatar')}
            accept="image/jpeg,image/png"
            placeholder={t(
              'admin:agent.strategy.form.avatarHelp',
              'Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)'
            )}
            value={avatarFiles}
            onChange={handleAvatarChange}
            mediaOnly={true}
            showPreview={true}
            height="h-32"
          />
        </div>

        <Divider />

        {/* Provider Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.provider', 'Loại Provider')}
          </Typography>

          <ResponsiveGrid
            maxColumns={{ xs: 2, sm: 3, md: 4, lg: 6, xl: 6 }}
            maxColumnsWithChatPanel={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 6 }}
            gap={{ xs: 4, md: 5, lg: 6 }}
          >
            {PROVIDERS.map(provider => (
              <ProviderCard
                key={provider.type}
                provider={provider.type}
                name={provider.name}
                isSelected={selectedProvider === provider.type}
                onClick={handleProviderSelect}
                disabled={isSubmitting}
              />
            ))}
          </ResponsiveGrid>
        </div>
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.resources', 'Tài nguyên')}
          </Typography>

          <FormGrid columns={1} gap="md">
            <FormItem
              name="modelId"
              label={t('admin:agent.strategy.form.model', 'Model')}
              required
            >
              <Select
                fullWidth
                loading={loadingSystemModels}
                disabled={!selectedProvider}
                placeholder={t('admin:agent.strategy.form.selectModel', 'Chọn model')}
                options={systemModelOptions}
                onChange={value => {
                  handleModelSelect(value as string);
                  // Cập nhật form value
                  if (formRef.current) {
                    formRef.current.setValues({ modelId: value });
                  }
                }}
              />
            </FormItem>
          </FormGrid>
        </div>
        <Divider />

        {/* Model Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.strategy.form.modelConfig', 'Cấu hình Model')}
          </Typography>

          {selectedModel ? (
            <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
              {/* Temperature - hiển thị nếu model hỗ trợ */}
              {selectedModel.samplingParameters.includes('temperature') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.form.temperature', 'Temperature')}
                  </label>
                  <Slider
                    value={modelConfig['temperature'] || 1}
                    min={0}
                    max={2}
                    step={0.1}
                    onValueChange={value => handleModelConfigChange('temperature', value)}
                    valueSuffix=""
                  />
                </div>
              )}

              {/* Top P - hiển thị nếu model hỗ trợ */}
              {selectedModel.samplingParameters.includes('top_p') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.form.topP', 'Top P')}
                  </label>
                  <Slider
                    value={modelConfig['top_p'] || 1}
                    min={0}
                    max={1}
                    step={0.1}
                    onValueChange={value => handleModelConfigChange('top_p', value)}
                    valueSuffix=""
                  />
                </div>
              )}

              {/* Top K - hiển thị nếu model hỗ trợ */}
              {selectedModel.samplingParameters.includes('top_k') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.form.topK', 'Top K')}
                  </label>
                  <Slider
                    value={modelConfig['top_k'] || 1}
                    min={1}
                    max={100}
                    step={1}
                    onValueChange={value => handleModelConfigChange('top_k', value)}
                    valueSuffix=""
                  />
                </div>
              )}

              {/* Max Tokens */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-foreground">
                  {t('admin:agent.form.maxTokens', 'Max Tokens')}
                </label>
                <Slider
                  value={modelConfig['max_tokens'] || 1000}
                  min={100}
                  max={selectedModel?.maxTokens ? parseInt(selectedModel.maxTokens) : 8000}
                  step={100}
                  onValueChange={value => handleModelConfigChange('max_tokens', value)}
                  valueSuffix=""
                />
                {selectedModel?.maxTokens && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {t('admin:agent.template.maxTokensLimit', 'Model limit')}: {selectedModel.maxTokens}
                  </div>
                )}
              </div>
            </FormGrid>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Typography variant="body2">
                {t(
                  'admin:agent.supervisor.form.selectModelFirst',
                  'Vui lòng chọn model để cấu hình parameters'
                )}
              </Typography>
            </div>
          )}
        </div>

        <Divider />

        {/* Model Selection */}

        {/* Content Steps */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:agent.strategy.form.content', 'Nội dung các bước')}
            </Typography>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addContentStep}
              disabled={isSubmitting}
            >
              <Icon name="plus" size="sm" className="mr-2" />
              {t('admin:agent.strategy.form.addStep', 'Thêm bước')}
            </Button>
          </div>

          <div className="space-y-3">
            {contentSteps.map((step, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="flex-1">
                  <div className="space-y-2">
                    <Typography
                      variant="body2"
                      className="font-medium text-gray-700 dark:text-gray-300"
                    >
                      Bước {step.stepOrder}
                    </Typography>
                    <Textarea
                      fullWidth
                      rows={3}
                      placeholder={t(
                        'admin:agent.strategy.form.contentPlaceholder',
                        'Nhập nội dung bước {step}',
                        { step: step.stepOrder }
                      )}
                      value={step.content}
                      onChange={e => updateContentStep(index, e.target.value)}
                    />
                  </div>
                </div>
                {contentSteps.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeContentStep(index)}
                    disabled={isSubmitting}
                    className="text-red-500 hover:text-red-600 mt-6"
                  >
                    <Icon name="trash" size="sm" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Example Steps */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:agent.strategy.form.exampleDefault', 'Ví dụ mặc định')}
            </Typography>
          </div>
          <Divider />

          <div className="space-y-3">
            {exampleSteps.map((step, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="flex-1">
                  <div className="space-y-2">
                    <Typography
                      variant="body2"
                      className="font-medium text-gray-700 dark:text-gray-300"
                    >
                      Ví dụ {step.stepOrder}
                    </Typography>
                    <Textarea
                      fullWidth
                      rows={3}
                      placeholder={t(
                        'admin:agent.strategy.form.examplePlaceholder',
                        'Nhập ví dụ cho bước {step}',
                        { step: step.stepOrder }
                      )}
                      value={step.content}
                      onChange={e => updateExampleStep(index, e.target.value)}
                    />
                  </div>
                </div>
               
              </div>
            ))}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-border">
          <IconCard
            icon="x"
            title={t('admin:agent.strategy.cancel')}
            onClick={onCancel}
            variant="secondary"
            disabled={isSubmitting}
          />
          <IconCard
            icon="check"
            title={t('admin:agent.strategy.form.create')}
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            isLoading={isSubmitting}
          />
        </div>
      </Form>
    </Card>
  );
};

export default AddAgentStrategyForm;
