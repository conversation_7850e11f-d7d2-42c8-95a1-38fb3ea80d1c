import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Icon,
  Slider,

  ResponsiveGrid,
  Loading,
  IconCard,
} from '@/shared/components/common';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { z } from 'zod';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { getProviderIcon } from '@/modules/admin/integration/provider-model/types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { apiClient } from '@/shared/api';

// Import types and hooks
import { UpdateAgentSupervisorParams, AgentSupervisorDetail } from '../agent-supervisor/types/agent-supervisor.types';
import {
  useUpdateAgentSupervisor
} from '../agent-supervisor/hooks/use-agent-supervisor';
import { useAdminSystemModels, AdminSystemModel } from '../hooks/useAdminSystemModels';
import AdminAgentSupervisorMCPConfig, { MCPConfigData } from './AdminAgentSupervisorMCPConfig';
import AdminFileConfig, { FileConfigData } from './AdminFileConfig';

interface EditAgentSupervisorFormProps {
  agentId: string;
  onSuccess?: () => void;
  onCancel: () => void;
}

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false,
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-foreground">{name}</div>
      </div>
    </Card>
  );
};

// Schema validation
const updateAgentSupervisorSchema = (t: any) =>
  z.object({
    name: z
      .string()
      .min(1, t('admin:agent.supervisor.form.validation.nameRequired', 'Agent Supervisor name is required'))
      .trim(),
    instruction: z
      .string()
      .min(1, t('admin:agent.supervisor.form.validation.instructionRequired', 'Instructions are required'))
      .trim(),
    modelId: z
      .string()
      .min(1, t('admin:agent.supervisor.form.validation.modelRequired', 'Model is required')),
  });

const EditAgentSupervisorForm: React.FC<EditAgentSupervisorFormProps> = ({
  agentId,
  onSuccess,
  onCancel,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const { success: showSuccess, error: showError } = useSmartNotification();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Agent data state
  const [agentData, setAgentData] = useState<AgentSupervisorDetail | null>(null);

  // Avatar upload states
  const [avatarFiles, setAvatarFiles] = useState<FileWithMetadata[]>([]);

  // Model config states
  const [modelConfig, setModelConfig] = useState<Record<string, number>>({
    temperature: 1,
    top_p: 1,
    top_k: 1,
    max_tokens: 1000,
  });

  // Provider and model selection states
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum>(
    TypeProviderEnum.OPENAI
  );
  const [selectedModel, setSelectedModel] = useState<AdminSystemModel | null>(null);

  // MCP configuration state
  const [mcpConfigData, setMCPConfigData] = useState<MCPConfigData>({
    mcpSystemIds: [],
  });

  // File configuration state
  const [fileConfigData, setFileConfigData] = useState<FileConfigData>({
    fileIds: [],
  });

  // Fetch system models using hook
  const { data: systemModelsResponse, isLoading: loadingSystemModels } = useAdminSystemModels({
    page: 1,
    limit: 40,
    sortBy: 'systemModels.modelId',
    provider: selectedProvider,
    enabled: true,
  });

  // Load agent detail
  const loadAgentDetail = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.get(`/admin/agent-supervisor/${agentId}`, {
        tokenType: 'admin',
      });
      // FIX: Backend có thể trả về flat object, không phải nested trong result
      const agent = (response.result || response) as AgentSupervisorDetail;
      setAgentData(agent);

      // Set model config from agent data với fallback values
      if (agent.modelConfig) {
        setModelConfig({
          temperature: agent.modelConfig.temperature ?? 1,
          top_p: agent.modelConfig.top_p ?? 1,
          top_k: agent.modelConfig.top_k ?? 1,
          max_tokens: agent.modelConfig.max_tokens ?? 1000,
        });
      } else {
        // Fallback nếu không có modelConfig
        setModelConfig({
          temperature: 1,
          top_p: 1,
          top_k: 1,
          max_tokens: 1000,
        });
      }

      // Set provider based on agent's provider
      const providerEnum =
        Object.values(TypeProviderEnum).find(
          p => p.toLowerCase() === agent.model?.provider?.toLowerCase()
        ) || TypeProviderEnum.OPENAI;
      setSelectedProvider(providerEnum);

      // Set MCP config data from agent data
      if (agent.mcp && agent.mcp.length > 0) {
        setMCPConfigData({
          mcpSystemIds: agent.mcp.map(mcp => mcp.id),
        });
      }

      // Set file config data from agent data
      if (agent.fileIds && agent.fileIds.length > 0) {
        setFileConfigData({
          fileIds: agent.fileIds,
        });
      }
    } catch (error) {
      console.error('Error loading agent detail:', error);
      // Sử dụng showError trực tiếp mà không cần trong dependencies
      showError({
        message: t('admin:agent.supervisor.error.loadFailed', 'Failed to load agent supervisor details'),
      });
    } finally {
      setIsLoading(false);
    }
  }, [agentId, t]); // Remove showError từ dependencies

  // Update agent supervisor mutation
  const updateAgentSupervisorMutation = useUpdateAgentSupervisor();

  // Available providers
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
    { type: TypeProviderEnum.GEMINI, name: 'Gemini' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
    { type: TypeProviderEnum.XAI, name: 'xAI' },
  ];

  // Load agent detail on component mount
  useEffect(() => {
    loadAgentDetail();
  }, [agentId]); // Chỉ depend on agentId, không depend on loadAgentDetail function

  // Set selected model khi systemModelsResponse và agentData đều có sẵn
  useEffect(() => {
    if (systemModelsResponse?.items && agentData?.model?.id && !selectedModel) {
      const model = systemModelsResponse.items.find(m => m.id === agentData.model.id);
      if (model) {
        setSelectedModel(model);
        console.log('🔄 Set selected model from agent detail:', model);
      } else {
        console.warn('⚠️ Model not found in system models:', agentData.model.id);
        console.log('Available models:', systemModelsResponse.items.map(m => ({ id: m.id, name: m.modelName })));
      }
    }
  }, [systemModelsResponse, agentData, selectedModel]);



  // Handle provider selection
  const handleProviderSelect = useCallback((provider: TypeProviderEnum) => {
    setSelectedProvider(provider);
    setSelectedModel(null); // Reset model when provider changes
    // Reset model config về default
    setModelConfig({
      temperature: 1,
      top_p: 1,
      top_k: 1,
      max_tokens: 1000,
    });
  }, []);

  // Handle model selection
  const handleModelSelect = useCallback((modelId: string) => {
    const model = systemModelsResponse?.items?.find(m => m.id === modelId);
    setSelectedModel(model || null);

    if (model) {
      // Tạo modelConfig chỉ với các parameters mà model hỗ trợ
      const newModelConfig: Record<string, number> = {};

      if (model.samplingParameters.includes('temperature')) {
        newModelConfig['temperature'] = modelConfig['temperature'] || 1;
      }
      if (model.samplingParameters.includes('top_p')) {
        newModelConfig['top_p'] = modelConfig['top_p'] || 1;
      }
      if (model.samplingParameters.includes('top_k')) {
        newModelConfig['top_k'] = modelConfig['top_k'] || 1;
      }

      // Luôn thêm max_tokens với giá trị mặc định dựa trên maxTokens của model
      const maxTokensLimit = parseInt(model.maxTokens) || 8000;
      const defaultMaxTokens = Math.min(modelConfig['max_tokens'] || 1000, maxTokensLimit);
      newModelConfig['max_tokens'] = defaultMaxTokens;

      setModelConfig(newModelConfig);
    }
  }, [systemModelsResponse, modelConfig]);

  // Handle model config change
  const handleModelConfigChange = useCallback((key: string, value: number) => {
    // Validation đặc biệt cho max_tokens
    if (key === 'max_tokens' && selectedModel?.maxTokens) {
      const maxTokensLimit = parseInt(selectedModel.maxTokens);
      if (value > maxTokensLimit) {
        value = maxTokensLimit; // Giới hạn không vượt quá maxTokens của model
      }
    }

    setModelConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  }, [selectedModel]);

  // Handle MCP config save
  const handleMCPConfigSave = useCallback((data: MCPConfigData) => {
    setMCPConfigData(data);
  }, []);

  // Handle File config save
  const handleFileConfigSave = useCallback((data: FileConfigData) => {
    setFileConfigData(data);
  }, []);

  // Upload image file to S3 using presigned URL
  const uploadImageFile = async (file: File, presignedUrl: string) => {
    try {
      console.log('🔍 [uploadImageFile] Starting upload to S3...', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        url: presignedUrl.substring(0, 100) + '...', // Log only first 100 chars for security
      });

      const response = await fetch(presignedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type,
        },
        body: file,
      });

      console.log('🔍 [uploadImageFile] Response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries()),
      });

      if (!response.ok) {
        // Không cố gắng đọc response text vì có thể gây CORS error
        throw new Error(`S3 upload failed: ${response.status} ${response.statusText}`);
      }

      console.log('✅ [uploadImageFile] S3 upload successful');
      return true;
    } catch (error) {
      console.error('❌ [uploadImageFile] Upload error:', error);

      // Kiểm tra nếu lỗi là do CORS nhưng upload thực sự thành công
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.warn('⚠️ [uploadImageFile] CORS error detected, but upload might have succeeded');
        // Trong trường hợp này, chúng ta có thể coi như upload thành công
        // vì S3 presigned URL thường không cho phép đọc response từ cross-origin
        return true;
      }

      throw error;
    }
  };

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      setIsSubmitting(true);

      console.log('🚀 Starting update form submission...');
      console.log('📝 Form values:', values);
      console.log('📁 Avatar files:', avatarFiles);

      const submitData: UpdateAgentSupervisorParams = {
        name: values.name,
        instruction: values.instruction,
        modelId: values.modelId,
        modelConfig,
        mcpId: mcpConfigData.mcpSystemIds,
        fileIds: fileConfigData.fileIds,
        avatarMimeType: avatarFiles.length > 0 ? avatarFiles[0].file.type : undefined,
      };

      console.log('📤 Updating data:', submitData);

      const response = await updateAgentSupervisorMutation.mutateAsync({
        id: agentId,
        data: submitData,
      });

      console.log('✅ Agent supervisor updated:', response);
      console.log('🔍 Response structure:', {
        hasResponse: !!response,
        responseKeys: response ? Object.keys(response) : [],
        hasAvatarUrlUpload: !!(response as any)?.avatarUrlUpload,
        avatarUrlUpload: (response as any)?.avatarUrlUpload,
      });

      // Response trả về trực tiếp object với avatarUrlUpload
      const avatarUploadUrl = (response as any)?.avatarUrlUpload;
      console.log('🔍 Avatar upload URL found:', avatarUploadUrl);

      // Upload avatar if provided
      const allUploadPromises: Promise<void>[] = [];

      if (avatarFiles.length > 0 && avatarFiles[0]?.file && avatarUploadUrl) {
        console.log('� Starting avatar upload...');
        console.log('� Avatar file details:', {
          fileName: avatarFiles[0].file.name,
          fileSize: avatarFiles[0].file.size,
          fileType: avatarFiles[0].file.type,
        });
        console.log('🔍 Avatar upload URL:', avatarUploadUrl);

        const avatarFile = avatarFiles[0].file;
        const uploadUrl = avatarUploadUrl;

        const avatarUploadPromise = (async () => {
          console.log(`🔍 Uploading avatar:`, {
            fileName: avatarFile.name,
            fileSize: avatarFile.size,
            fileType: avatarFile.type,
            uploadUrl: uploadUrl,
          });

          try {
            await uploadImageFile(avatarFile, uploadUrl);
            console.log('✅ Avatar uploaded successfully');
          } catch (error) {
            console.error('❌ Exception uploading avatar:', error);
            throw error;
          }
        })();
        allUploadPromises.push(avatarUploadPromise);
      } else {
        console.log('⚠️ SKIPPING avatar upload:', {
          hasAvatarFiles: avatarFiles.length > 0,
          hasValidFile: !!avatarFiles[0]?.file,
          hasUploadUrl: !!avatarUploadUrl,
          avatarFilesCount: avatarFiles.length,
        });
      }

      // Đợi tất cả uploads hoàn thành
      if (allUploadPromises.length > 0) {
        try {
          await Promise.all(allUploadPromises);
          console.log('🎉 All uploads completed successfully');

          // Hiển thị thông báo thành công cho upload
          showSuccess({
            title: t('admin:agent.upload.success', 'Upload thành công'),
            message: t('admin:agent.upload.successMessage', 'Tất cả files đã được upload thành công'),
          });
        } catch (uploadError) {
          console.error('❌ Upload error:', uploadError);
          showError({
            title: t('admin:agent.upload.error', 'Lỗi upload'),
            message: t('admin:agent.upload.errorMessage', 'Có lỗi xảy ra khi upload files'),
          });
          throw uploadError;
        }
      }

      showSuccess({ message: t('admin:agent.supervisor.form.updateSuccess', 'Agent Supervisor updated successfully') });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('❌ Error in update form submission:', error);
      showError({
        message: error instanceof Error
          ? error.message
          : t('admin:agent.supervisor.form.updateError', 'Error occurred while updating Agent Supervisor')
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Default values for the form - sử dụng agentData nếu có, nếu không thì empty string
  const defaultValues = React.useMemo(
    () => ({
      name: agentData?.name || '',
      instruction: agentData?.instruction || '',
      modelId: agentData?.model?.id || '',
    }),
    [agentData]
  );

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loading />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Form
        key={agentData?.id} // Force re-render when agent data changes
        schema={updateAgentSupervisorSchema(t)}
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >

        {/* Basic Information */}
        <div className="space-y-4">
          

          <FormGrid columns={1} gap="md">
            <FormItem name="name" label={t('admin:agent.supervisor.form.name', 'Agent Supervisor Name')} required>
              <Input
                fullWidth
                placeholder={t('admin:agent.supervisor.form.namePlaceholder', 'Enter Agent Supervisor name')}
                disabled={isSubmitting}
              />
            </FormItem>

            

            <FormItem name="instruction" label={t('admin:agent.supervisor.form.instruction', 'Instructions')} required>
              <Textarea
                placeholder={t('admin:agent.supervisor.form.instructionPlaceholder', 'Enter instructions for Agent Supervisor')}
                rows={4}
                disabled={isSubmitting}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Avatar Upload */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.supervisor.form.avatar', 'Avatar')}
          </Typography>
          {agentData?.avatar && (
            <div className="mb-4">
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-2">
                {t('admin:agent.supervisor.form.currentAvatar', 'Current avatar')}:
              </Typography>
              <img
                src={agentData.avatar}
                alt={agentData.name}
                className="w-16 h-16 rounded-full object-cover"
              />
              <Typography variant="caption" className="text-gray-500 dark:text-gray-400 mt-1 block">
                {t('admin:agent.supervisor.form.currentAvatarNote', 'Upload new image to replace')}
              </Typography>
            </div>
          )}
          <MultiFileUpload
            value={avatarFiles}
            onChange={setAvatarFiles}
            accept="image/jpeg,image/png"
            placeholder={t('admin:agent.supervisor.form.avatarHelp', 'Supported formats: JPG, PNG (1 image only)')}
            mediaOnly={true}
          />
        </div>

        <Divider />

        {/* Provider Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.supervisor.form.provider', 'Provider Type')}
          </Typography>

          <ResponsiveGrid
            maxColumns={{ xs: 2, sm: 3, md: 4, lg: 6, xl: 6 }}
            maxColumnsWithChatPanel={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 6 }}
            gap={{ xs: 4, md: 5, lg: 6 }}
          >
            {providers.map(provider => (
              <ProviderCard
                key={provider.type}
                provider={provider.type}
                name={provider.name}
                isSelected={selectedProvider === provider.type}
                onClick={handleProviderSelect}
                disabled={isSubmitting}
              />
            ))}
          </ResponsiveGrid>
        </div>

        <Divider />

        {/* Model Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.supervisor.form.resources', 'Resources')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem name="modelId" label={t('admin:agent.supervisor.form.model', 'Model')} required>
              <Select
                fullWidth
                loading={loadingSystemModels}
                placeholder={t('admin:agent.supervisor.form.selectModel', 'Select model')}
                value={defaultValues.modelId}
                options={
                  systemModelsResponse?.items?.map(model => ({
                    value: model.id,
                    label: model.modelName,
                  })) || []
                }
                onChange={value => {
                  handleModelSelect(value as string);
                }}
              />
            </FormItem>
          </FormGrid>
        </div>

        {/* Model Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.supervisor.form.modelConfig', 'Model Configuration')}
          </Typography>

          {selectedModel ? (
            <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
              {/* Temperature */}
              {selectedModel.samplingParameters.includes('temperature') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.supervisor.form.temperature', 'Temperature')}
                  </label>
                  <Slider
                    value={modelConfig['temperature'] || 1}
                    min={0}
                    max={2}
                    step={0.1}
                    onValueChange={value => handleModelConfigChange('temperature', value)}
                    valueSuffix=""
                  />
                </div>
              )}

              {/* Top P */}
              {selectedModel.samplingParameters.includes('top_p') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.supervisor.form.topP', 'Top P')}
                  </label>
                  <Slider
                    value={modelConfig['top_p'] || 1}
                    min={0}
                    max={1}
                    step={0.1}
                    onValueChange={value => handleModelConfigChange('top_p', value)}
                    valueSuffix=""
                  />
                </div>
              )}

              {/* Top K */}
              {selectedModel.samplingParameters.includes('top_k') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.supervisor.form.topK', 'Top K')}
                  </label>
                  <Slider
                    value={modelConfig['top_k'] || 1}
                    min={1}
                    max={100}
                    step={1}
                    onValueChange={value => handleModelConfigChange('top_k', value)}
                    valueSuffix=""
                  />
                </div>
              )}

              {/* Max Tokens */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-foreground">
                  {t('admin:agent.supervisor.form.maxTokens', 'Max Tokens')}
                </label>
                <Slider
                  value={modelConfig['max_tokens'] || 1000}
                  min={100}
                  max={selectedModel?.maxTokens ? parseInt(selectedModel.maxTokens) : 8000}
                  step={100}
                  onValueChange={value => handleModelConfigChange('max_tokens', value)}
                  valueSuffix=""
                />
                {selectedModel?.maxTokens && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {t('admin:agent.template.maxTokensLimit', 'Model limit')}: {selectedModel.maxTokens}
                  </div>
                )}
              </div>

            </FormGrid>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Typography variant="body2">
                {t(
                  'admin:agent.supervisor.form.selectModelFirst',
                  'Please select a model to configure parameters'
                )}
              </Typography>
            </div>
          )}
        </div>

        <Divider />

        {/* MCP Systems Configuration */}
        <AdminAgentSupervisorMCPConfig
          initialData={mcpConfigData}
          onSave={handleMCPConfigSave}
          mode="edit"
          agentSupervisorId={agentId}
        />

        <Divider />

        {/* Knowledge Files Configuration */}
        <AdminFileConfig
          initialData={fileConfigData}
          onSave={handleFileConfigSave}
          mode="edit"
          agentSupervisorId={agentId}
        />

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 ">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            disabled={isSubmitting}
          />
          <IconCard
            icon="check"
            title={t('admin:agent.supervisor.form.update')}
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            isLoading={isSubmitting}
          />
        </div>
      </Form>
    </div>
  );
};

export default EditAgentSupervisorForm;
