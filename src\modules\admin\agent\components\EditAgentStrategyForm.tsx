import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Icon,
  Slider,
  IconCard,
} from '@/shared/components/common';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { z } from 'zod';
import { apiClient } from '@/shared/api/axios';
import { useAdminAgentNotification } from '../hooks/useAdminAgentNotification';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { getProviderIcon } from '@/modules/admin/integration/provider-model/types';

// Import the correct types from service
import {
  StrategyContent,
  StrategyExample,
  AgentStrategyDetail,
  UpdateAgentStrategyParams,
  UpdateAgentStrategyResponse,
  ModelConfig,
} from '../agent-strategy/types/agent-strategy.types';
import { useAdminSystemModels, AdminSystemModel } from '../hooks/useAdminSystemModels';

// Types

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false,
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-foreground">{name}</div>
      </div>
    </Card>
  );
};

interface EditAgentStrategyFormProps {
  agentStrategy: AgentStrategyDetail;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Schema validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const updateAgentStrategySchema = (t: any) =>
  z.object({
    name: z
      .string()
      .min(1, t('admin:agent.strategy.validation.nameRequired', 'Tên chiến lược là bắt buộc'))
      .trim(),
    instruction: z
      .string()
      .min(1, t('admin:agent.strategy.validation.instructionRequired', 'Hướng dẫn là bắt buộc'))
      .trim(),
    modelId: z
      .string()
      .min(1, t('admin:agent.strategy.validation.modelRequired', 'Model là bắt buộc'))
      .uuid(
        t(
          'admin:agent.strategy.validation.modelRequired',
          'Model ID phải là UUID hợp lệ'
        )
      ),
  });

const EditAgentStrategyForm: React.FC<EditAgentStrategyFormProps> = ({
  agentStrategy,
  onCancel,
  onSuccess,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const notification = useAdminAgentNotification();

  // Avatar upload states
  const [avatarFiles, setAvatarFiles] = useState<FileWithMetadata[]>([]);

  // Model config states - Initialize with values from API response or defaults
  const [modelConfig, setModelConfig] = useState<Record<string, number>>(() => {
    // Load existing modelConfig from API response if available
    const existingConfig = (agentStrategy.modelConfig as unknown as Record<string, number>) || {};
    console.log('🔍 [EditAgentStrategyForm] Initial modelConfig:', {
      existingConfig,
      agentStrategyModelConfig: agentStrategy.modelConfig
    });

    return {
      temperature: existingConfig.temperature || 0.7,
      max_tokens: existingConfig.max_tokens || 2000,
      top_p: existingConfig.top_p || 0.95,
      top_k: existingConfig.top_k || 0.8,
    };
  });

  // Content and example states - Initialize with existing data
  const [contentSteps, setContentSteps] = useState<StrategyContent[]>(
    agentStrategy.content && agentStrategy.content.length > 0
      ? agentStrategy.content
      : [{ stepOrder: 1, content: '' }]
  );
  const [exampleSteps, setExampleSteps] = useState<StrategyExample[]>(
    agentStrategy.exampleDefault && agentStrategy.exampleDefault.length > 0
      ? agentStrategy.exampleDefault
      : [{ stepOrder: 1, content: '' }]
  );

  // System models states
  const [selectedModel, setSelectedModel] = useState<AdminSystemModel | null>(null);

  // Determine provider from existing model
  const getProviderFromModelId = (modelId: string): TypeProviderEnum => {
    if (modelId.includes('gpt')) return TypeProviderEnum.OPENAI;
    if (modelId.includes('claude')) return TypeProviderEnum.ANTHROPIC;
    if (modelId.includes('gemini')) return TypeProviderEnum.GEMINI;
    if (modelId.includes('llama')) return TypeProviderEnum.GEMINI;
    if (modelId.includes('deepseek')) return TypeProviderEnum.DEEPSEEK;
    if (modelId.includes('grok')) return TypeProviderEnum.XAI;
    return TypeProviderEnum.OPENAI; // default
  };

  const initialModelId = agentStrategy.modelId || (agentStrategy as any).mdoelId || agentStrategy.modelSystemId || '';
  const initialProvider = getProviderFromModelId(initialModelId);

  console.log('🔍 [EditAgentStrategyForm] Initial setup:', {
    agentStrategyModelId: agentStrategy.modelId,
    agentStrategyMdoelId: (agentStrategy as any).mdoelId,
    agentStrategyModelSystemId: agentStrategy.modelSystemId,
    initialModelId,
    initialProvider,
    agentStrategyName: agentStrategy.name
  });

  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum>(initialProvider);

  // Fetch all system models first to find the existing model
  const { data: allSystemModelsResponse, isLoading: loadingAllSystemModels } = useAdminSystemModels({
    page: 1,
    limit: 100,
    sortBy: 'systemModels.modelId',
    enabled: true, // Always enabled for edit mode
  });

  // Then fetch models for selected provider for display
  const { data: providerSystemModelsResponse, isLoading: loadingProviderSystemModels } = useAdminSystemModels({
    page: 1,
    limit: 40,
    sortBy: 'systemModels.modelId',
    provider: selectedProvider,
    enabled: true,
  });

  // Use all models for finding existing model, provider models for display
  const finalSystemModelsResponse = allSystemModelsResponse;
  const loadingSystemModels = loadingAllSystemModels || loadingProviderSystemModels;

  // Default form values - Initialize with existing data
  const defaultValues = {
    name: agentStrategy.name || '',
    instruction: agentStrategy.instruction || '',
    modelId: agentStrategy.modelId || (agentStrategy as any).mdoelId || agentStrategy.modelSystemId || '',
  };

  console.log('🔍 [EditAgentStrategyForm] Default values:', defaultValues);

  // Handle provider selection
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    setSelectedProvider(provider);
    setSelectedModel(null);
    // Reset model config về default
    setModelConfig({
      temperature: 0.7,
      max_tokens: 2000,
      top_p: 0.95,
      top_k: 0.8,
    });
  };

  // Handle model selection
  const handleModelSelect = (modelId: string) => {
    const model = finalSystemModelsResponse?.items?.find((m: any) => m.id === modelId);
    setSelectedModel(model || null);

    if (model) {
      // Tạo modelConfig chỉ với các parameters mà model hỗ trợ
      const newModelConfig: Record<string, number> = {};

      if (model.samplingParameters.includes('temperature')) {
        newModelConfig['temperature'] = 0.7;
      }
      if (model.samplingParameters.includes('top_p')) {
        newModelConfig['top_p'] = 0.95;
      }
      if (model.samplingParameters.includes('top_k')) {
        newModelConfig['top_k'] = 0.8;
      }
      // Luôn thêm max_tokens với giá trị mặc định dựa trên maxTokens của model
      const maxTokensLimit = parseInt(model.maxTokens) || 8000;
      const defaultMaxTokens = Math.min(2000, maxTokensLimit); // Mặc định 2000 hoặc maxTokens nếu nhỏ hơn
      newModelConfig['max_tokens'] = defaultMaxTokens;

      setModelConfig(newModelConfig);

      console.log('🔍 [EditAgentStrategyForm] Model selected:', {
        modelId: model.modelName,
        samplingParameters: model.samplingParameters,
        newModelConfig,
      });
    }
  };

  // Effect để set selectedModel khi có systemModelsResponse và agentStrategy.modelId
  useEffect(() => {
    const modelIdToUse = agentStrategy.modelId || (agentStrategy as any).mdoelId || agentStrategy.modelSystemId;
    console.log('🔍 [EditAgentStrategyForm] useEffect triggered:', {
      modelIdToUse,
      hasSystemModelsResponse: !!finalSystemModelsResponse?.items,
      systemModelsCount: finalSystemModelsResponse?.items?.length || 0,
      selectedProvider,
      selectedModel: selectedModel?.id
    });

    if (finalSystemModelsResponse?.items && modelIdToUse) {
      const model = finalSystemModelsResponse.items.find(m => m.id === modelIdToUse);
      console.log('🔍 [EditAgentStrategyForm] Looking for model:', {
        modelIdToUse,
        foundModel: !!model,
        modelName: model?.modelName,
        modelProvider: model?.provider,
        allModelIds: finalSystemModelsResponse.items.map(m => ({ id: m.id, name: m.modelName, provider: m.provider }))
      });

      if (model && !selectedModel) {
        setSelectedModel(model);
        console.log('🔍 [EditAgentStrategyForm] Found existing model:', model);

        // Update provider based on model data
        if (model.provider) {
          const providerEnum = model.provider.toUpperCase() as TypeProviderEnum;
          if (Object.values(TypeProviderEnum).includes(providerEnum)) {
            setSelectedProvider(providerEnum);
            console.log('🔍 [EditAgentStrategyForm] Updated provider to:', providerEnum);
          }
        }

        // Load existing modelConfig values for this model
        if (agentStrategy.modelConfig) {
          const existingConfig = agentStrategy.modelConfig as unknown as Record<string, number>;
          const newModelConfig: Record<string, number> = {};

          // Only set values for parameters that the model supports
          if (model.samplingParameters.includes('temperature') && existingConfig.temperature !== undefined) {
            newModelConfig['temperature'] = existingConfig.temperature;
          }
          if (model.samplingParameters.includes('top_p') && existingConfig.top_p !== undefined) {
            newModelConfig['top_p'] = existingConfig.top_p;
          }
          if (model.samplingParameters.includes('top_k') && existingConfig.top_k !== undefined) {
            newModelConfig['top_k'] = existingConfig.top_k;
          }
          // Luôn load max_tokens nếu có
          if (existingConfig.max_tokens !== undefined) {
            const maxTokensLimit = parseInt(model.maxTokens) || 8000;
            // Đảm bảo giá trị không vượt quá giới hạn của model
            newModelConfig['max_tokens'] = Math.min(existingConfig.max_tokens, maxTokensLimit);
          }

          // Update modelConfig with existing values
          setModelConfig(prev => ({
            ...prev,
            ...newModelConfig,
          }));

          console.log('🔍 [EditAgentStrategyForm] Loaded existing modelConfig:', newModelConfig);
        }
      }
    }
  }, [finalSystemModelsResponse, agentStrategy.modelId, agentStrategy.modelSystemId, agentStrategy.modelConfig, selectedModel]);

  // Handle model config changes
  const handleModelConfigChange = (key: keyof typeof modelConfig, value: number) => {
    // Validation đặc biệt cho max_tokens
    if (key === 'max_tokens' && selectedModel?.maxTokens) {
      const maxTokensLimit = parseInt(selectedModel.maxTokens);
      if (value > maxTokensLimit) {
        value = maxTokensLimit; // Giới hạn không vượt quá maxTokens của model
      }
    }

    setModelConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Danh sách providers
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
    { type: TypeProviderEnum.GEMINI, name: 'Google' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
    { type: TypeProviderEnum.XAI, name: 'XAI' },
  ];

  // Handle avatar file selection - chỉ cho phép 1 ảnh
  const handleAvatarChange = useCallback((files: FileWithMetadata[]) => {
    if (files.length > 0 && files[0]) {
      setAvatarFiles([files[0]]);
    } else {
      setAvatarFiles([]);
    }
  }, []);

  // Handle content steps
  const addContentStep = () => {
    const newStep: StrategyContent = {
      stepOrder: contentSteps.length + 1,
      content: '',
    };
    setContentSteps([...contentSteps, newStep]);

    // Đồng bộ thêm example step
    const newExampleStep: StrategyExample = {
      stepOrder: exampleSteps.length + 1,
      content: '',
    };
    setExampleSteps([...exampleSteps, newExampleStep]);
  };

  const removeContentStep = (index: number) => {
    if (contentSteps.length > 1) {
      const newSteps = contentSteps.filter((_, i) => i !== index);
      const reorderedSteps = newSteps.map((step, i) => ({
        ...step,
        stepOrder: i + 1,
      }));
      setContentSteps(reorderedSteps);

      // Đồng bộ xóa example step
      if (exampleSteps.length > 1) {
        const newExampleSteps = exampleSteps.filter((_, i) => i !== index);
        const reorderedExampleSteps = newExampleSteps.map((step, i) => ({
          ...step,
          stepOrder: i + 1,
        }));
        setExampleSteps(reorderedExampleSteps);
      }
    }
  };

  const updateContentStep = (index: number, content: string) => {
    const newSteps = [...contentSteps];
    if (newSteps[index]) {
      newSteps[index] = {
        ...newSteps[index],
        content,
        stepOrder: newSteps[index].stepOrder || index + 1,
      };
      setContentSteps(newSteps);
    }
  };

  // Handle example steps
  const addExampleStep = () => {
    const newStep: StrategyExample = {
      stepOrder: exampleSteps.length + 1,
      content: '',
    };
    setExampleSteps([...exampleSteps, newStep]);
  };

  const removeExampleStep = (index: number) => {
    if (exampleSteps.length > 1) {
      const newSteps = exampleSteps.filter((_, i) => i !== index);
      const reorderedSteps = newSteps.map((step, i) => ({
        ...step,
        stepOrder: i + 1,
      }));
      setExampleSteps(reorderedSteps);
    }
  };

  const updateExampleStep = (index: number, content: string) => {
    const newSteps = [...exampleSteps];
    if (newSteps[index]) {
      newSteps[index] = {
        ...newSteps[index],
        content,
        stepOrder: newSteps[index].stepOrder || index + 1,
      };
      setExampleSteps(newSteps);
    }
  };

  // Upload image file to S3 using presigned URL với method PUT
  const uploadImageToS3 = async (file: File, presignedUrl: string): Promise<void> => {
    console.log(`🔍 [uploadImageToS3] Starting upload:`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      uploadUrl: presignedUrl,
    });

    const response = await fetch(presignedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type,
      },
      body: file,
    });

    console.log(`🔍 [uploadImageToS3] Response status:`, response.status);
    console.log(`🔍 [uploadImageToS3] Response ok:`, response.ok);

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unable to read error response');
      console.error(`❌ [uploadImageToS3] Upload failed:`, {
        status: response.status,
        statusText: response.statusText,
        errorText,
      });
      throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
    }

    console.log('✅ [uploadImageToS3] Upload successful');
  };

  // Handle form submission
  const handleFormSubmit = async (values: Record<string, unknown>) => {
    console.log('🔍 [EditAgentStrategyForm] Form submitted with values:', values);
    console.log('🔍 [EditAgentStrategyForm] Model config:', modelConfig);
    console.log('🔍 [EditAgentStrategyForm] Avatar files:', avatarFiles);
    console.log('🔍 [EditAgentStrategyForm] Avatar files details:', {
      length: avatarFiles.length,
      firstFile: avatarFiles[0]
        ? {
            name: avatarFiles[0].file.name,
            size: avatarFiles[0].file.size,
            type: avatarFiles[0].file.type,
          }
        : null,
    });
    console.log('🔍 [EditAgentStrategyForm] Content steps:', contentSteps);
    console.log('🔍 [EditAgentStrategyForm] Example steps:', exampleSteps);

    // Validate content and examples
    const validContentSteps = contentSteps.filter(step => step.content.trim() !== '');
    const validExampleSteps = exampleSteps.filter(step => step.content.trim() !== '');

    if (validContentSteps.length === 0) {
      notification.validationError(
        t('admin:agent.strategy.validation.contentRequired', 'Nội dung các bước là bắt buộc')
      );
      return;
    }

    if (validExampleSteps.length === 0) {
      notification.validationError(
        t('admin:agent.strategy.validation.exampleRequired', 'Ví dụ mặc định là bắt buộc')
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Tạo modelConfig chỉ với các parameters mà model hỗ trợ
      const filteredModelConfig: Record<string, number> = {};

      if (selectedModel) {
        if (
          selectedModel.samplingParameters.includes('temperature') &&
          modelConfig['temperature'] !== undefined
        ) {
          filteredModelConfig['temperature'] = modelConfig['temperature'];
        }
        if (
          selectedModel.samplingParameters.includes('top_p') &&
          modelConfig['top_p'] !== undefined
        ) {
          filteredModelConfig['top_p'] = modelConfig['top_p'];
        }
        if (
          selectedModel.samplingParameters.includes('top_k') &&
          modelConfig['top_k'] !== undefined
        ) {
          filteredModelConfig['top_k'] = modelConfig['top_k'];
        }
        // Luôn thêm max_tokens nếu có
        if (modelConfig['max_tokens'] !== undefined) {
          const maxTokensLimit = parseInt(selectedModel.maxTokens) || 8000;
          // Đảm bảo max_tokens không vượt quá giới hạn của model
          const validMaxTokens = Math.min(modelConfig['max_tokens'], maxTokensLimit);
          filteredModelConfig['max_tokens'] = validMaxTokens;
        }
      }

      console.log('🔍 [EditAgentStrategyForm] Filtered modelConfig:', {
        selectedModel: selectedModel?.modelName,
        samplingParameters: selectedModel?.samplingParameters,
        originalModelConfig: modelConfig,
        filteredModelConfig,
      });

      // Prepare form data theo đúng API spec
      const strategyData: UpdateAgentStrategyParams = {
        name: values['name'] as string,
        instruction: values['instruction'] as string,
        modelConfig: filteredModelConfig as unknown as ModelConfig,
        content: validContentSteps,
        exampleDefault: validExampleSteps,
        modelId: values['modelId'] as string,
      };

      // Thêm avatarMimeType nếu có file
      if (avatarFiles.length > 0 && avatarFiles[0]) {
        strategyData.avatarMimeType = avatarFiles[0].file.type;
      }

      console.log('🔍 [EditAgentStrategyForm] Updating strategy data:', strategyData);

      // Submit form to API với method PUT
      const response = await apiClient.put<UpdateAgentStrategyResponse>(
        `/admin/agent-strategy/${agentStrategy.id}`,
        strategyData,
        {
          tokenType: 'admin',
        }
      );

      console.log('🔍 [EditAgentStrategyForm] Update response:', response);

      // Upload avatar if provided and we have upload URL
      if (avatarFiles.length > 0 && avatarFiles[0] && response.result?.avatarUploadUrl) {
        console.log(
          '🔍 [EditAgentStrategyForm] Starting avatar upload with URL:',
          response.result.avatarUploadUrl
        );
        notification.info({
          message: t('admin:agent.strategy.form.uploadingAvatar', 'Đang tải lên avatar...'),
          duration: 2000,
        });

        try {
          await uploadImageToS3(avatarFiles[0].file, response.result.avatarUploadUrl);
          console.log('✅ [EditAgentStrategyForm] Avatar upload completed successfully');
          notification.success({
            message: t(
              'admin:agent.strategy.form.uploadAvatarSuccess',
              'Tải lên avatar thành công'
            ),
          });
        } catch (uploadErr) {
          console.error('❌ [EditAgentStrategyForm] Avatar upload failed:', uploadErr);
          notification.error({
            message: t(
              'admin:agent.strategy.form.uploadAvatarError',
              'Có lỗi xảy ra khi tải lên avatar'
            ),
          });
          // Không throw error ở đây để form vẫn được coi là thành công
        }
      } else {
        console.log('🔍 [EditAgentStrategyForm] No avatar upload needed:', {
          hasAvatarFiles: avatarFiles.length > 0,
          hasFile: !!avatarFiles[0],
          hasUploadUrl: !!response.result?.avatarUploadUrl,
          responseResult: response.result,
        });
      }

      // Show success message
      notification.success({
        message: t('admin:agent.strategy.form.updateSuccess', 'Cập nhật chiến lược thành công'),
      });

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error('❌ Error updating strategy:', err);
      notification.error({
        message: t(
          'admin:agent.strategy.form.updateError',
          'Có lỗi xảy ra khi cập nhật chiến lược'
        ),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <Typography variant="h5" className="font-semibold">
            {t('admin:agent.strategy.editStrategy', 'Chỉnh sửa Chiến lược')}
          </Typography>
          <Button variant="ghost" size="sm" onClick={onCancel} disabled={isSubmitting}>
            <Icon name="x" size="sm" />
          </Button>
        </div>
      </div>

      <Form
        schema={updateAgentStrategySchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.strategy.form.basicInfo', 'Thông tin cơ bản')}
          </Typography>

          <FormGrid columns={1} gap="md">
            <FormItem
              name="name"
              label={t('admin:agent.strategy.form.name', 'Tên Chiến lược')}
              required
            >
              <Input
                fullWidth
                placeholder={t('admin:agent.strategy.form.namePlaceholder', 'Nhập tên chiến lược')}
              />
            </FormItem>

            <FormItem
              name="instruction"
              label={t('admin:agent.strategy.form.instruction', 'Hướng dẫn')}
              required
            >
              <Textarea
                fullWidth
                rows={4}
                placeholder={t(
                  'admin:agent.strategy.form.instructionPlaceholder',
                  'Nhập hướng dẫn cho chiến lược'
                )}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Avatar Upload */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.strategy.form.avatar', 'Avatar')}
          </Typography>

          {agentStrategy.avatar && (
            <div className="mb-4">
              <Typography variant="body2" className="mb-2">
                {t('admin:agent.strategy.form.currentAvatar', 'Avatar hiện tại')}:
              </Typography>
              <img
                src={agentStrategy.avatar}
                alt="Current avatar"
                className="w-20 h-20 rounded-lg object-cover"
              />
            </div>
          )}

          <MultiFileUpload
            label={t('admin:agent.strategy.form.avatarUpload', 'Tải lên avatar')}
            accept="image/jpeg,image/png"
            placeholder={t(
              'admin:agent.strategy.form.avatarHelp',
              'Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)'
            )}
            value={avatarFiles}
            onChange={handleAvatarChange}
            mediaOnly={true}
            showPreview={true}
            height="h-32"
          />
        </div>

        <Divider />

        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.provider', 'Loại Provider')}
          </Typography>

          <div className="flex flex-nowrap gap-3 overflow-x-auto pb-2">
            {providers.map(provider => (
              <ProviderCard
                key={provider.type}
                provider={provider.type}
                name={provider.name}
                isSelected={selectedProvider === provider.type}
                onClick={handleProviderSelect}
                disabled={isSubmitting}
              />
            ))}
          </div>
        </div>
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.resources', 'Tài nguyên')}
          </Typography>

          <FormGrid columns={1} gap="md">
            <FormItem
              name="modelId"
              label={t('admin:agent.strategy.form.model', 'Model')}
              required
            >
              <Select
                fullWidth
                loading={loadingSystemModels}
                placeholder={t('admin:agent.strategy.form.selectModel', 'Chọn model')}
                value={defaultValues.modelId}
                options={
                  (providerSystemModelsResponse?.items || finalSystemModelsResponse?.items)?.map((model: any) => ({
                    value: model.id,
                    label: model.modelName,
                  })) || []
                }
                onChange={value => {
                  handleModelSelect(value as string);
                }}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Model Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.strategy.form.modelConfig', 'Cấu hình Model')}
          </Typography>

          {selectedModel ? (
            <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
              {/* Temperature - hiển thị nếu model hỗ trợ */}
              {selectedModel.samplingParameters.includes('temperature') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.form.temperature', 'Temperature')}
                  </label>
                  <Slider
                    value={modelConfig['temperature'] || 0.7}
                    min={0}
                    max={2}
                    step={0.1}
                    onValueChange={value => handleModelConfigChange('temperature', value)}
                    valueSuffix=""
                  />
                </div>
              )}

              {/* Top P - hiển thị nếu model hỗ trợ */}
              {selectedModel.samplingParameters.includes('top_p') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.form.topP', 'Top P')}
                  </label>
                  <Slider
                    value={modelConfig['top_p'] || 0.95}
                    min={0}
                    max={1}
                    step={0.1}
                    onValueChange={value => handleModelConfigChange('top_p', value)}
                    valueSuffix=""
                  />
                </div>
              )}

              {/* Top K - hiển thị nếu model hỗ trợ */}
              {selectedModel.samplingParameters.includes('top_k') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.form.topK', 'Top K')}
                  </label>
                  <Slider
                    value={modelConfig['top_k'] || 0.8}
                    min={1}
                    max={100}
                    step={1}
                    onValueChange={value => handleModelConfigChange('top_k', value)}
                    valueSuffix=""
                  />
                </div>
              )}

              {/* Max Tokens */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-foreground">
                  {t('admin:agent.form.maxTokens', 'Max Tokens')}
                </label>
                <Slider
                  value={modelConfig['max_tokens'] || 2000}
                  min={100}
                  max={selectedModel?.maxTokens ? parseInt(selectedModel.maxTokens) : 8000}
                  step={100}
                  onValueChange={value => handleModelConfigChange('max_tokens', value)}
                  valueSuffix=""
                />
                {selectedModel?.maxTokens && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {t('admin:agent.template.maxTokensLimit', 'Model limit')}: {selectedModel.maxTokens}
                  </div>
                )}
              </div>
            </FormGrid>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Typography variant="body2">
                {t(
                  'admin:agent.strategy.form.selectModelFirst',
                  'Vui lòng chọn model để cấu hình parameters'
                )}
              </Typography>
            </div>
          )}
        </div>

        <Divider />

        {/* Provider Selection */}

        {/* Model Selection */}

        {/* Content Steps */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:agent.strategy.form.content', 'Nội dung các bước')}
            </Typography>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addContentStep}
              disabled={isSubmitting}
            >
              <Icon name="plus" size="sm" className="mr-2" />
              {t('admin:agent.strategy.form.addStep', 'Thêm bước')}
            </Button>
          </div>

          <div className="space-y-3">
            {contentSteps.map((step, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="flex-1">
                  <div className="space-y-2">
                    <Typography
                      variant="body2"
                      className="font-medium text-gray-700 dark:text-gray-300"
                    >
                      {t('admin:agent.strategy.form.step', 'Bước')} {step.stepOrder}
                    </Typography>
                    <Textarea
                      fullWidth
                      rows={3}
                      placeholder={t(
                        'admin:agent.strategy.form.contentPlaceholder',
                        'Nhập nội dung bước {step}',
                        { step: step.stepOrder }
                      )}
                      value={step.content}
                      onChange={e => updateContentStep(index, e.target.value)}
                    />
                  </div>
                </div>
                {contentSteps.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeContentStep(index)}
                    disabled={isSubmitting}
                    className="text-red-500 hover:text-red-600 mt-6"
                  >
                    <Icon name="trash" size="sm" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        <Divider />

        {/* Example Steps */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:agent.strategy.form.exampleDefault', 'Ví dụ mặc định')}
            </Typography>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addExampleStep}
              disabled={isSubmitting}
            >
              <Icon name="plus" size="sm" className="mr-2" />
              {t('admin:agent.strategy.form.addExample', 'Thêm ví dụ')}
            </Button>
          </div>

          <div className="space-y-3">
            {exampleSteps.map((step, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="flex-1">
                  <div className="space-y-2">
                    <Typography
                      variant="body2"
                      className="font-medium text-gray-700 dark:text-gray-300"
                    >
                      {t('admin:agent.strategy.form.example', 'Ví dụ')} {step.stepOrder}
                    </Typography>
                    <Textarea
                      fullWidth
                      rows={3}
                      placeholder={t(
                        'admin:agent.strategy.form.examplePlaceholder',
                        'Nhập ví dụ cho bước {step}',
                        { step: step.stepOrder }
                      )}
                      value={step.content}
                      onChange={e => updateExampleStep(index, e.target.value)}
                    />
                  </div>
                </div>
                {exampleSteps.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeExampleStep(index)}
                    disabled={isSubmitting}
                    className="text-red-500 hover:text-red-600 mt-6"
                  >
                    <Icon name="trash" size="sm" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-border">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            disabled={isSubmitting}
          />
          <IconCard
            icon="check"
            title={t('admin:agent.strategy.form.update')}
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            isLoading={isSubmitting}
          />
        </div>
      </Form>
    </Card>
  );
};

export default EditAgentStrategyForm;
