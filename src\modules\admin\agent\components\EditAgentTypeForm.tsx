import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Typography,
  Card,
  Divider,
  Checkbox,
  IconCard,
} from '@/shared/components/common';
import { z } from 'zod';
import AgentSystemConfig, { AgentSystemConfigData } from './AgentSystemConfig';
import RegistryConfig, { RegistryConfigData } from './RegistryConfig';
import { AgentConfigAccordionProvider } from '@/modules/ai-agents/contexts/AgentConfigAccordionContext.tsx';

// Import the correct types from service
import { UpdateTypeAgentParams, TypeAgentDetail } from '../agent-type/types/type-agent.types';

interface UpdateTypeAgentResponse {
  result?: {
    id: number;
  };
}

interface EditAgentTypeFormProps {
  agentType: TypeAgentDetail;
  onSubmit: (id: number, values: UpdateTypeAgentParams) => Promise<UpdateTypeAgentResponse>;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Schema validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const updateAgentTypeSchema = (t: any) => z.object({
  name: z.string()
    .min(1, t('admin:agent.validation.nameRequired', 'Tên loại agent là bắt buộc'))
    .trim(),
  description: z.string()
    .min(1, t('admin:agent.validation.descriptionRequired', 'Mô tả là bắt buộc'))
    .trim(),
});

const EditAgentTypeForm: React.FC<EditAgentTypeFormProps> = ({
  agentType,
  onSubmit,
  onCancel,
  onSuccess
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Ref để track xem đã initialize chưa
  const initializedRef = useRef(false);
  const agentTypeIdRef = useRef<number | null>(null);

  // Debug log để xem agentType có gì (chỉ log khi cần thiết)
  // console.log('🔍 EditAgentTypeForm - agentType:', agentType);

  // Agent Systems config state - khởi tạo empty, sẽ set sau
  const [agentSystemsConfig, setAgentSystemsConfig] = useState<AgentSystemConfigData>({
    agentSystems: [],
    toolIds: [],
  });

  // Registry config state - khởi tạo empty, sẽ set sau
  const [registryConfig, setRegistryConfig] = useState<RegistryConfigData>({
    modelRegistryIds: [],
  });

  // isAllModel state - khởi tạo từ agentType
  const [isAllModel, setIsAllModel] = useState<boolean>(false);
  
  // Default config states - khởi tạo empty, sẽ set trong useEffect
  const [defaultConfig, setDefaultConfig] = useState({
    enableProfileCustomization: false,
    enableTool: false,
    enableOutputMessenger: false,
    enableOutputLivechat: false,
    enableOutputZaloOa: false,
    enableOutputPayment: false,
    enableConvert: false,
    enableResources: false,
    enableShipment: false,
    enableMultiAgent: false,
    enableStrategy: false,
  });

  // Initialize data chỉ 1 lần khi agentType thay đổi
  useEffect(() => {
    if (!agentType || agentTypeIdRef.current === agentType.id) {
      return; // Không làm gì nếu cùng agentType
    }

    console.log('🔄 Initializing EditAgentTypeForm for agentType:', agentType.id);

    // Update refs
    agentTypeIdRef.current = agentType.id;
    initializedRef.current = true;

    // Set agent systems config
    setAgentSystemsConfig({
      agentSystems: agentType.agentSystems || [],
      toolIds: agentType.toolIds || [],
    });

    // Set registry config
    setRegistryConfig({
      modelRegistryIds: agentType.modelRegistryIds || [],
    });

    // Set isAllModel
    setIsAllModel(agentType.isAllModel || false);

    // Set default config - sử dụng đúng tên field
    setDefaultConfig({
      enableProfileCustomization: agentType.enableProfileCustomization ?? false,
      enableTool: agentType.enableTool ?? false,
      enableOutputMessenger: agentType.enableOutputMessenger ?? false,
      enableOutputLivechat: agentType.enableOutputLivechat ?? false,
      enableOutputZaloOa: agentType.enableOutputZaloOa ?? false,
      enableOutputPayment: agentType.enableOutputPayment ?? false,
      enableConvert: agentType.enableConvert ?? false,
      enableResources: agentType.enableResources ?? false,
      enableShipment: agentType.enableShipment ?? false,
      enableMultiAgent: agentType.enableMultiAgent ?? false,
      enableStrategy: agentType.enableStrategy ?? false,
    });
  }, [agentType.id]); // Chỉ depend vào agentType.id

  // Agent Systems config state - khởi tạo từ agentType
  // const [agentSystemsConfig, setAgentSystemsConfig] = useState<AgentSystemConfigData>(() => {
  //   console.log('EditAgentTypeForm - agentType.agentSystems:', agentType.agentSystems);

  //   // Đảm bảo agentSystems luôn là array of strings
  //   let agentSystems: string[] = [];
  //   if (Array.isArray(agentType.agentSystems)) {
  //     agentSystems = agentType.agentSystems.map(item =>
  //       typeof item === 'string' ? item : (item as any)?.id || String(item)
  //     );
  //   }

  //   console.log('EditAgentTypeForm - processed agentSystems:', agentSystems);

  //   return {
  //     agentSystems,
  //   };
  // });

  // Default values for the form - khởi tạo từ agentType với useMemo để tránh re-render
  const defaultValues = useMemo(() => ({
    name: agentType.name,
    description: agentType.description,
  }), [agentType.name, agentType.description]);

  // Handle default config changes - sử dụng useCallback để tránh re-render
  const handleDefaultConfigChange = useCallback((key: string, value: boolean) => {
    setDefaultConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  // Handle agent systems config changes - sử dụng useCallback để tránh re-render
  const handleAgentSystemsConfigChange = useCallback((data: AgentSystemConfigData) => {
    setAgentSystemsConfig(data);
  }, []);

  // Handle registry config changes - sử dụng useCallback để tránh re-render
  const handleRegistryConfigChange = useCallback((data: RegistryConfigData) => {
    setRegistryConfig(data);
  }, []);

  // Handle form submission
  const handleFormSubmit = async (values: Record<string, unknown>) => {
    console.log('🔍 [EditAgentTypeForm] handleFormSubmit called with values:', values);
    setIsSubmitting(true);

    try {
      // Debug log current state
      console.log('🔍 [EditAgentTypeForm] agentSystemsConfig:', agentSystemsConfig);
      console.log('🔍 [EditAgentTypeForm] defaultConfig:', defaultConfig);

      // Validate agent systems - temporarily commented out for testing
      // if (agentSystemsConfig.agentSystems.length === 0) {
      //   console.error('At least one agent system is required');
      //   setIsSubmitting(false);
      //   return;
      // }

      // Prepare form data - sử dụng đúng tên field mà backend mong đợi
      const agentTypeData: UpdateTypeAgentParams = {
        name: values['name'] as string,
        description: values['description'] as string,
        // Sử dụng đúng tên field như backend mong đợi
        enableProfileCustomization: defaultConfig.enableProfileCustomization,
        enableTool: defaultConfig.enableTool,
        enableOutputMessenger: defaultConfig.enableOutputMessenger,
        enableOutputLivechat: defaultConfig.enableOutputLivechat,
        enableOutputZaloOa: defaultConfig.enableOutputZaloOa,
        enableOutputPayment: defaultConfig.enableOutputPayment,
        enableConvert: defaultConfig.enableConvert,
        enableResources: defaultConfig.enableResources,
        enableShipment: defaultConfig.enableShipment,
        enableMultiAgent: defaultConfig.enableMultiAgent,
        enableStrategy: defaultConfig.enableStrategy,
        // agentSystems: agentSystemsConfig.agentSystems,
        // toolIds: agentSystemsConfig.toolIds,
        // isAllModel,
        // modelRegistryIds: isAllModel ? [] : registryConfig.modelRegistryIds,
      };

      console.log('Updating agent type data:', agentTypeData);

      // Submit form data
      const updateResult = await onSubmit(agentType.id, agentTypeData);
      console.log('Agent type updated successfully:', updateResult);

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Error updating agent type form:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };



  // Memoized props cho AgentSystemConfig để tránh re-render
  const agentSystemConfigProps = useMemo(() => ({
    initialData: agentSystemsConfig,
    onSave: handleAgentSystemsConfigChange,
    mode: 'edit' as const,
    agentTypeId: agentType.id.toString(),
  }), [agentSystemsConfig, handleAgentSystemsConfigChange, agentType.id]);

  // Memoized props cho RegistryConfig để tránh re-render
  const registryConfigProps = useMemo(() => ({
    initialData: registryConfig,
    onSave: handleRegistryConfigChange,
    mode: 'edit' as const,
    agentTypeId: agentType.id.toString(),
  }), [registryConfig, handleRegistryConfigChange, agentType.id]);

  return (
    <AgentConfigAccordionProvider>
      <Card>
        <div className="flex justify-start items-center mb-6">
          <Typography variant="h4" className="font-semibold">
            {t('admin:agent.type.editType', 'Chỉnh sửa Loại Agent')}
          </Typography>
        </div>

      <Form
        schema={updateAgentTypeSchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.basicInfo', 'Thông tin cơ bản')}
          </Typography>

          <FormItem
            name="name"
            label={t('admin:agent.form.name', 'Tên Loại Agent')}
            required
          >
            <Input
              fullWidth
              placeholder={t('admin:agent.form.namePlaceholder', 'Nhập tên loại agent')}
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('admin:agent.form.description', 'Mô tả')}
            required
          >
            <Textarea
              fullWidth
              rows={3}
              placeholder={t('admin:agent.form.descriptionPlaceholder', 'Nhập mô tả loại agent')}
            />
          </FormItem>

         
        </div>

        <Divider />

        {/* Agent Systems Configuration */}
        


        {/* Default Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.defaultConfig', 'Cấu hình mặc định')}
          </Typography>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Checkbox
              checked={defaultConfig?.enableProfileCustomization || false}
              onChange={(checked) => handleDefaultConfigChange('enableProfileCustomization', checked)}
              label={t('admin:agent.form.enableProfileCustomization', 'Cho phép tùy chỉnh hồ sơ agent')}
            />
            <Checkbox
              checked={defaultConfig?.enableOutputMessenger || false}
              onChange={(checked) => handleDefaultConfigChange('enableOutputMessenger', checked)}
              label={t('admin:agent.form.enableOutputMessenger', 'Cho phép xuất ra Messenger')}
            />
            <Checkbox
              checked={defaultConfig?.enableOutputLivechat || false}
              onChange={(checked) => handleDefaultConfigChange('enableOutputLivechat', checked)}
              label={t('admin:agent.form.enableOutputLivechat', 'Cho phép xuất ra Website Live Chat')}
            />
            <Checkbox
              checked={defaultConfig?.enableConvert || false}
              onChange={(checked) => handleDefaultConfigChange('enableConvert', checked)}
              label={t('admin:agent.form.enableConvert', 'Theo dõi chuyển đổi tác vụ')}
            />
            <Checkbox
              checked={defaultConfig?.enableResources || false}
              onChange={(checked) => handleDefaultConfigChange('enableResources', checked)}
              label={t('admin:agent.form.enableResources', 'Sử dụng tài nguyên')}
            />
            <Checkbox
              checked={defaultConfig?.enableStrategy || false}
              onChange={(checked) => handleDefaultConfigChange('enableStrategy', checked)}
              label={t('admin:agent.form.enableStrategy', 'Thực thi chiến lược động')}
            />
            <Checkbox
              checked={defaultConfig?.enableMultiAgent || false}
              onChange={(checked) => handleDefaultConfigChange('enableMultiAgent', checked)}
              label={t('admin:agent.form.enableMultiAgent', 'Hợp tác đa agent')}
            />
            <Checkbox
              checked={defaultConfig?.enableOutputZaloOa || false}
              onChange={(checked) => handleDefaultConfigChange('enableOutputZaloOa', checked)}
              label={t('admin:agent.form.enableOutputZaloOa', 'Cho phép xuất ra Zalo OA')}
            />
            <Checkbox
              checked={defaultConfig?.enableOutputPayment || false}
              onChange={(checked) => handleDefaultConfigChange('enableOutputPayment', checked)}
              label={t('admin:agent.form.enableOutputPayment', 'Cho phép xuất ra Payment')}
            />
            <Checkbox
              checked={defaultConfig?.enableTool || false}
              onChange={(checked) => handleDefaultConfigChange('enableTool', checked)}
              label={t('admin:agent.form.enableTool', 'Cho phép xuất ra Tool')}
            />
            <Checkbox
              checked={defaultConfig?.enableShipment || false}
              onChange={(checked) => handleDefaultConfigChange('enableShipment', checked)}
              label={t('admin:agent.form.enableShipment', 'Cho phép xuất ra Shipment')}
            />
          </div>
        </div>

        <Divider />

        {/* Agent Systems Configuration */}
        <div className="space-y-4">
          <AgentSystemConfig {...agentSystemConfigProps} />
        </div>

        <Divider />

        {/* Model Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.modelConfiguration', 'Model Configuration')}
          </Typography>

          <Checkbox
            checked={isAllModel}
            onChange={(checked) => setIsAllModel(checked)}
            label={t('admin:agent.form.isAllModel', 'Sử dụng tất cả models')}
          />

          {!isAllModel && (
            <div className="mt-4">
              <RegistryConfig {...registryConfigProps} />
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6">
          <IconCard
            icon="x"
            title={t('admin:agent.common.cancel', 'Hủy')}
            onClick={onCancel}
            variant="secondary"
            disabled={isSubmitting}
          />
          <IconCard
            icon="check"
            title={t('admin:agent.updateType', 'Cập nhật Loại Agent')}
            type='submit'
            variant="primary"
            disabled={isSubmitting}
            isLoading={isSubmitting}
            onClick={() => {
              console.log('🔍 [EditAgentTypeForm] IconCard clicked!');
            }}
          />
        </div>
        
      </Form>
    </Card>
    </AgentConfigAccordionProvider>
  );
};

export default EditAgentTypeForm;
