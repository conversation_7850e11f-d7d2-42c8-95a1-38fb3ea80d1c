import { initReactI18next } from 'react-i18next';
import i18n from 'i18next';

import enTranslation from '../locales/en.json';
import viTranslation from '../locales/vi.json';
import zhTranslation from '../locales/zh.json';

// Import module translations
import businessResources from '../modules/business/locales';
import authResources from '../modules/auth/locales';
import adminAuthResources from '../modules/admin/auth/locales';
import adminBusinessResources from '../modules/admin/business/locales';
import profileResources from '../modules/profile/locales';
import componentsResources from '../modules/components/locales';
import marketingResources from '../modules/marketing/locales';
import integrationResources from '../modules/integration/locales';
import { subscriptionResources } from '../modules/subscription/locales';
import { subscriptionResources as adminSubscriptionResources } from '../modules/admin/subscription/locales';
// Import SMS locales directly from JSON files to avoid circular dependency
import smsLocalesVi from '../modules/marketing/sms/locales/vi.json';
import smsLocalesEn from '../modules/marketing/sms/locales/en.json';
import smsLocalesZh from '../modules/marketing/sms/locales/zh.json';
import marketplaceResources from '../modules/marketplace/locales';
import rpointResources from '../modules/rpoint/locales';
import aiAgentsResources from '../modules/ai-agents/locales';
import employeeResources from '../modules/admin/employee/locales';
import { rpointAdminResources as rpointAdminResources } from '@/modules/admin/r-point/locales';
import affiliateResources from '@/modules/admin/affiliate/locales';
import userResources from '@/modules/admin/user/locales';
import { adminHelpResources } from '@/modules/admin/help/locales';
import dataResources from '@/modules/admin/data/locales';
import moduleDataResources from '@/modules/data/locales';
import marketplaceAdminResources from '@/modules/admin/marketplace/locales';
import marketingAdminResources from '@/modules/admin/marketing/locales';
import blogResources from '@/modules/blog/locales';
import blogAdminResources from '@/modules/admin/blog/locales';
import agentResources from '@/modules/admin/agent/locales';
import adminContractResources from '@/modules/admin/contract/locales';
import adminConfigResources from '@/modules/admin/config/locales';

import settingsResources from '@/modules/settings/locales';
import adminIntegrationResources from '@/modules/admin/integration/locales';
import contractResources from '@/modules/contract/locales';
import contractAffiliateResources from '@/modules/contract-affiliate/locales';
import { userAffiliateResources } from '@/modules/user/affiliate/locales';
import userDatasetResources from '@/modules/user-dataset/locales';
import modelResources from '@/modules/model/locales';

import calendarResources from '@/modules/calendar/locales';
import threadsResources from '@/modules/threads/locales';
import chatResources from '@/shared/locales/chat';
import adminEnTranslation from './i18n/locales/en/admin.json';
import adminViTranslation from './i18n/locales/vi/admin.json';
import adminZhTranslation from './i18n/locales/zh/admin.json';

import integrationsEnTranslation from './i18n/locales/en/integrations.json';
import integrationsViTranslation from './i18n/locales/vi/integrations.json';
import integrationsZhTranslation from './i18n/locales/zh/integrations.json';
import paymentEnTranslation from './i18n/locales/en/payment.json';
import paymentViTranslation from './i18n/locales/vi/payment.json';
import paymentZhTranslation from './i18n/locales/zh/payment.json';
import commonEnTranslation from './i18n/locales/en/common.json';
import commonViTranslation from './i18n/locales/vi/common.json';
import commonZhTranslation from './i18n/locales/zh/common.json';
import paginationEnTranslation from './i18n/locales/en/pagination.json';
import paginationViTranslation from './i18n/locales/vi/pagination.json';
import paginationZhTranslation from './i18n/locales/zh/pagination.json';
import adminDatasetResources from '@/modules/admin/dataset/locales';
import adminToolResources from '@/modules/admin/tool/locales';
import toolsResources from '@/modules/tools/locales';
import dashboardResources from '@/modules/dashboard/locales';
import { usageLocales } from '@/modules/usage/locales';

export const resources = {
  en: {
    translation: enTranslation,
    business: businessResources.en.business,
    auth: authResources.en.auth,
    authValidation: authResources.en.validation,
    adminAuth: adminAuthResources.en.adminAuth,
    adminValidation: adminAuthResources.en.validation,
    profile: profileResources.en.profile,
    validation: profileResources.en.validation,
    components: componentsResources.en.components,
    marketing: marketingResources.en.marketing,
    sms: smsLocalesEn.sms,
    integration: integrationResources.en.integration,
    subscription: subscriptionResources.en.subscription,
    marketplace: marketplaceResources.en.marketplace,
    rpoint: rpointResources.en.rpoint,
    aiAgents: aiAgentsResources.en.aiAgents,
    employee: employeeResources.en.employee,
    rpointAdmin: rpointAdminResources.en.rpoint,
    affiliate: affiliateResources.en.affiliate,
    user: userResources.en.user,
    data: moduleDataResources.en.data,
    blog: blogResources.en,
    blogAdmin: blogAdminResources.en.blogAdmin,

    settings: settingsResources.en.settings,
    contract: contractResources.en.contract,
    'contract-affiliate': contractAffiliateResources.en['contract-affiliate'],
    userAffiliate: userAffiliateResources.en.userAffiliate,
    'user-dataset': userDatasetResources.en['user-dataset'],
    'admin-dataset': adminDatasetResources.en['admin-dataset'],
    model: modelResources.en.model,
    'admin-tool': adminToolResources.en['admin-tool'],
    calendar: calendarResources.en.calendar,
    threads: threadsResources.en.threads,
    chat: chatResources.en.chat,
    admin: {
      ...adminEnTranslation,
      ...adminBusinessResources.en.admin,
      ...adminHelpResources.en.admin,
      data: dataResources.en.data,
      marketplace: marketplaceAdminResources.en.marketplace,
      marketing: marketingAdminResources.en.marketingAdmin,
      agent: agentResources.en.agent,
      memories: agentResources.en.memories,
      integration: adminIntegrationResources.en.adminIntegration,
      subscription: adminSubscriptionResources.en.subscription,
      contract: adminContractResources.en.contract,
      config: adminConfigResources.en.config,
    },
    integrations: integrationsEnTranslation,
    payment: paymentEnTranslation,
    common: commonEnTranslation,
    pagination: paginationEnTranslation,
    tools: toolsResources.en.tools,
    dashboard: dashboardResources.en.dashboard,
    usage: usageLocales.en.usage,
  },
  vi: {
    translation: viTranslation,
    business: businessResources.vi.business,
    auth: authResources.vi.auth,
    authValidation: authResources.vi.validation,
    adminAuth: adminAuthResources.vi.adminAuth,
    adminValidation: adminAuthResources.vi.validation,
    profile: profileResources.vi.profile,
    validation: profileResources.vi.validation,
    components: componentsResources.vi.components,
    marketing: marketingResources.vi.marketing,
    sms: smsLocalesVi.sms,
    integration: integrationResources.vi.integration,
    subscription: subscriptionResources.vi.subscription,
    marketplace: marketplaceResources.vi.marketplace,
    rpoint: rpointResources.vi.rpoint,
    aiAgents: aiAgentsResources.vi.aiAgents,
    employee: employeeResources.vi.employee,
    rpointAdmin: rpointAdminResources.vi.rpoint,
    affiliate: affiliateResources.vi.affiliate,
    user: userResources.vi.user,
    data: moduleDataResources.vi.data,
    blog: blogResources.vi,
    blogAdmin: blogAdminResources.vi.blogAdmin,

    settings: settingsResources.vi.settings,
    contract: contractResources.vi.contract,
    'contract-affiliate': contractAffiliateResources.vi['contract-affiliate'],
    userAffiliate: userAffiliateResources.vi.userAffiliate,
    'user-dataset': userDatasetResources.vi['user-dataset'],
    'admin-dataset': adminDatasetResources.vi['admin-dataset'],
    model: modelResources.vi.model,
    'admin-tool': adminToolResources.vi['admin-tool'],
    calendar: calendarResources.vi.calendar,
    threads: threadsResources.vi.threads,
    chat: chatResources.vi.chat,
    admin: {
      ...adminViTranslation,
      ...adminBusinessResources.vi.admin,
      ...adminHelpResources.vi.admin,
      data: dataResources.vi.data,
      marketplace: marketplaceAdminResources.vi.marketplace,
      marketing: marketingAdminResources.vi.marketingAdmin,
      agent: agentResources.vi.agent,
      memories: agentResources.vi.memories,
      integration: adminIntegrationResources.vi.adminIntegration,
      subscription: adminSubscriptionResources.vi.subscription,
      contract: adminContractResources.vi.contract,
      config: adminConfigResources.vi.config,
    },
    integrations: integrationsViTranslation,
    payment: paymentViTranslation,
    common: commonViTranslation,
    pagination: paginationViTranslation,
    tools: toolsResources.vi.tools,
    dashboard: dashboardResources.vi.dashboard,
    usage: usageLocales.vi.usage,
  },
  zh: {
    translation: zhTranslation,
    business: businessResources.zh.business,
    auth: authResources.zh.auth,
    authValidation: authResources.zh.validation,
    adminAuth: adminAuthResources.zh.adminAuth,
    adminValidation: adminAuthResources.zh.validation,
    profile: profileResources.zh.profile,
    validation: profileResources.zh.validation,
    components: componentsResources.zh.components,
    marketing: marketingResources.zh.marketing,
    sms: smsLocalesZh.sms,
    integration: integrationResources.zh.integration,
    subscription: subscriptionResources.zh.subscription,
    marketplace: marketplaceResources.zh.marketplace,
    rpoint: rpointResources.zh.rpoint,
    aiAgents: aiAgentsResources.zh.aiAgents,
    employee: employeeResources.zh.employee,
    rpointAdmin: rpointAdminResources.zh.rpoint,
    affiliate: affiliateResources.zh.affiliate,
    user: userResources.zh.user,
    data: moduleDataResources.zh.data,
    blog: blogResources.zh,
    blogAdmin: blogAdminResources.zh.blogAdmin,

    settings: settingsResources.zh.settings,
    contract: contractResources.zh.contract,
    'contract-affiliate': contractAffiliateResources.zh['contract-affiliate'],
    userAffiliate: userAffiliateResources.zh.userAffiliate,
    'user-dataset': userDatasetResources.zh['user-dataset'],
    'admin-dataset': adminDatasetResources.zh['admin-dataset'],
    model: modelResources.zh.model,
    'admin-tool': adminToolResources.zh['admin-tool'],
    calendar: calendarResources.zh.calendar,
    threads: threadsResources.zh.threads,
    chat: chatResources.zh.chat,
    admin: {
      ...adminZhTranslation,
      ...adminBusinessResources.zh.admin,
      ...adminHelpResources.zh.admin,
      data: dataResources.zh.data,
      marketplace: marketplaceAdminResources.zh.marketplace,
      marketing: marketingAdminResources.zh.marketingAdmin,
      agent: agentResources.zh.agent,
      memories: agentResources.zh.memories,
      integration: adminIntegrationResources.zh.adminIntegration,
      subscription: adminSubscriptionResources.zh.subscription,
      contract: adminContractResources.zh.contract,
      config: adminConfigResources.zh.config,
    },
    integrations: integrationsZhTranslation,
    payment: paymentZhTranslation,
    common: commonZhTranslation,
    pagination: paginationZhTranslation,
    tools: toolsResources.zh.tools,
    dashboard: dashboardResources.zh.dashboard,
    usage: usageLocales.zh.usage,
  },
};

export const availableLanguages = [
  { code: 'vi', name: 'Tiếng Việt' },
  { code: 'en', name: 'English' },
  { code: 'zh', name: '中文' },
];

i18n.use(initReactI18next).init({
  resources,
  lng: 'vi', // Default language
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false, // React already escapes values
  },
});

export default i18n;
