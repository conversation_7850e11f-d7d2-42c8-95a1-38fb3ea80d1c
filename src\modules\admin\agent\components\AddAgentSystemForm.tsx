import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Icon,
  Slider,
  IconCard,
  ResponsiveGrid,
} from '@/shared/components/common';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { z } from 'zod';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { getProviderIcon } from '@/modules/admin/integration/provider-model/types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

// Import the correct types from service
import { CreateAgentSystemParams } from '../agent-system/types/agent-system.types';
import { useAdminSystemModels, AdminSystemModel } from '../hooks/useAdminSystemModels';
import AdminMCPConfig, { MCPConfigData } from './AdminMCPConfig';

// Types - extend với description field
interface CreateAgentSystemDto extends CreateAgentSystemParams {
  description?: string; // Thêm description field
}

interface CreateAgentSystemResponse {
  code?: number;
  message?: string;
  result?: {
    id: string;
    avatarUrlUpload?: string; // Theo response structure bạn cung cấp
  };
}




interface AddAgentSystemFormProps {
  onSubmit: (values: CreateAgentSystemDto) => Promise<CreateAgentSystemResponse>;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false,
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-foreground">{name}</div>
      </div>
    </Card>
  );
};

// Schema validation - thêm lại description và sửa modelId validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createAgentSystemSchema = (t: any) =>
  z.object({
    name: z
      .string()
      .min(1, t('admin:agent.validation.nameRequired', 'Tên agent là bắt buộc'))
      .trim(),
    instruction: z
      .string()
      .min(1, t('admin:agent.validation.instructionRequired', 'Hướng dẫn là bắt buộc'))
      .trim(),
    description: z
      .string()
      .min(1, t('admin:agent.validation.descriptionRequired', 'Mô tả là bắt buộc'))
      .trim(),
    vectorStoreId: z.string().optional(),
    modelId: z
      .string()
      .min(1, t('admin:agent.validation.modelRequired', 'Model là bắt buộc'))
      .uuid(t('admin:agent.validation.modelIdInvalid', 'Model ID phải là UUID hợp lệ')),
  });

const AddAgentSystemForm: React.FC<AddAgentSystemFormProps> = ({
  onSubmit,
  onCancel,
  onSuccess,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const { success: showSuccess, error: showError } = useSmartNotification();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Avatar upload states
  const [avatarFiles, setAvatarFiles] = useState<FileWithMetadata[]>([]);

  // Model config states
  const [modelConfig, setModelConfig] = useState<Record<string, number>>({
    temperature: 1,
    top_p: 1,
    top_k: 1,
    max_tokens: 1000,
  });

  // Provider and model selection states
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum>(
    TypeProviderEnum.OPENAI
  );
  const [selectedModel, setSelectedModel] = useState<AdminSystemModel | null>(null);

  // MCP configuration state
  const [mcpConfigData, setMCPConfigData] = useState<MCPConfigData>({
    mcpSystemIds: [],
  });

  // Fetch system models using hook
  const { data: systemModelsResponse, isLoading: loadingSystemModels } = useAdminSystemModels({
    page: 1,
    limit: 40,
    sortBy: 'systemModels.modelId',
    provider: selectedProvider,
    enabled: true,
  });

  // Default values for the form - thêm lại description
  const defaultValues = React.useMemo(
    () => ({
      name: '',
      instruction: '',
      description: '',
      modelId: '',
    }),
    []
  );



  // Handle provider selection
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    setSelectedProvider(provider);
    setSelectedModel(null);
    // Reset model config về default
    setModelConfig({
      temperature: 1,
      top_p: 1,
      top_k: 1,
      max_tokens: 1000,
    });
  };

  // Handle model selection
  const handleModelSelect = (modelId: string) => {
    const model = systemModelsResponse?.items?.find(m => m.id === modelId);
    setSelectedModel(model || null);

    if (model) {
      // Tạo modelConfig chỉ với các parameters mà model hỗ trợ
      const newModelConfig: Record<string, number> = {};

      if (model.samplingParameters.includes('temperature')) {
        newModelConfig['temperature'] = 1;
      }
      if (model.samplingParameters.includes('top_p')) {
        newModelConfig['top_p'] = 1;
      }
      if (model.samplingParameters.includes('top_k')) {
        newModelConfig['top_k'] = 1;
      }

      // Luôn thêm max_tokens với giá trị mặc định dựa trên maxTokens của model
      const maxTokensLimit = parseInt(model.maxTokens) || 8000;
      const defaultMaxTokens = Math.min(1000, maxTokensLimit); // Mặc định 1000 hoặc maxTokens nếu nhỏ hơn
      newModelConfig['max_tokens'] = defaultMaxTokens;

      setModelConfig(newModelConfig);


    }
  };

  // Handle model config changes
  const handleModelConfigChange = (key: keyof typeof modelConfig, value: number) => {
    // Validation đặc biệt cho max_tokens
    if (key === 'max_tokens' && selectedModel?.maxTokens) {
      const maxTokensLimit = parseInt(selectedModel.maxTokens);
      if (value > maxTokensLimit) {
        value = maxTokensLimit; // Giới hạn không vượt quá maxTokens của model
      }
    }

    setModelConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle avatar file selection - chỉ cho phép 1 ảnh
  const handleAvatarChange = useCallback((files: FileWithMetadata[]) => {
    // Chỉ lấy file đầu tiên nếu có nhiều file
    if (files.length > 0 && files[0]) {
      setAvatarFiles([files[0]]);
    } else {
      setAvatarFiles([]);
    }
  }, []);

  // Handle MCP config save
  const handleMCPConfigSave = useCallback((data: MCPConfigData) => {
    setMCPConfigData(data);
    console.log('🔍 [AddAgentSystemForm] MCP config updated:', data);
  }, []);

  // Upload image file to S3 using presigned URL
  const uploadImageFile = async (file: File, presignedUrl: string) => {
    try {
      console.log('🔍 [uploadImageFile] Starting upload to S3...', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        url: presignedUrl.substring(0, 100) + '...', // Log only first 100 chars for security
      });

      const response = await fetch(presignedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type,
        },
        body: file,
      });

      console.log('🔍 [uploadImageFile] Response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
      });

      // S3 PUT thành công thường trả về status 200 hoặc 204
      if (response.ok) {
        console.log('✅ [uploadImageFile] S3 upload successful');
        return true;
      } else {
        throw new Error(`S3 upload failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('❌ [uploadImageFile] Upload error:', error);

      // Kiểm tra nếu lỗi là do CORS nhưng upload thực sự thành công
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.warn('⚠️ [uploadImageFile] CORS error detected, but upload might have succeeded');
        // Trong trường hợp này, chúng ta có thể coi như upload thành công
        // vì S3 presigned URL thường không cho phép đọc response từ cross-origin
        return true;
      }

      throw error;
    }
  };

  // Handle form submission
  const handleFormSubmit = async (values: Record<string, unknown>) => {
    setIsSubmitting(true);

    try {
      // Tạo modelConfig chỉ với các parameters mà model hỗ trợ
      const filteredModelConfig: Record<string, number> = {};

      if (selectedModel) {
        if (
          selectedModel.samplingParameters.includes('temperature') &&
          modelConfig['temperature'] !== undefined
        ) {
          filteredModelConfig['temperature'] = modelConfig['temperature'];
        }
        if (
          selectedModel.samplingParameters.includes('top_p') &&
          modelConfig['top_p'] !== undefined
        ) {
          filteredModelConfig['top_p'] = modelConfig['top_p'];
        }
        if (
          selectedModel.samplingParameters.includes('top_k') &&
          modelConfig['top_k'] !== undefined
        ) {
          filteredModelConfig['top_k'] = modelConfig['top_k'];
        }

        // Luôn thêm max_tokens nếu có
        if (modelConfig['max_tokens'] !== undefined) {
          const maxTokensLimit = parseInt(selectedModel.maxTokens) || 8000;
          // Đảm bảo max_tokens không vượt quá giới hạn của model
          const validMaxTokens = Math.min(modelConfig['max_tokens'], maxTokensLimit);
          filteredModelConfig['max_tokens'] = validMaxTokens;
        }
      }



      // Prepare form data - chỉ gửi những field được API chấp nhận
      const agentData: Record<string, unknown> = {
        name: values['name'] as string,
        nameCode: values['nameCode'] as string,
        avatarMimeType:
          avatarFiles.length > 0 && avatarFiles[0] ? avatarFiles[0].file.type : undefined,
        modelConfig: filteredModelConfig,
        instruction: values['instruction'] as string,
        description: values['description'] as string,
        vectorStoreId: (values['vectorStoreId'] as string) || undefined,
        // Thêm MCP systems nếu có
        mcpId: mcpConfigData.mcpSystemIds.length > 0 ? mcpConfigData.mcpSystemIds : undefined,
      };

      // Chỉ thêm modelId nếu có giá trị và là UUID hợp lệ
      if (values['modelId']) {
        agentData['modelId'] = values['modelId'] as string;
      }

      console.log('Submitting agent data:', agentData);

      // Submit form data
      const createResult = await onSubmit(agentData as unknown as CreateAgentSystemDto);
      console.log('Agent created successfully:', createResult);

      // Debug logging để kiểm tra response structure
      console.log('=== AVATAR UPLOAD DEBUG ===');
      console.log('Debug - avatarFiles.length:', avatarFiles.length);
      console.log('Debug - avatarFiles:', avatarFiles);
      console.log('Debug - createResult:', createResult);
      console.log('=== END DEBUG ===');

      // Response structure: { code: 200, message: "...", result: { id: "...", avatarUrlUpload: "..." } }
      const avatarUploadUrl = (createResult as any)?.result?.avatarUrlUpload || (createResult as any)?.avatarUrlUpload;
      console.log('🔍 Avatar upload URL found:', avatarUploadUrl);

      // Upload avatar if provided
      const allUploadPromises: Promise<void>[] = [];

      if (avatarFiles.length > 0 && avatarFiles[0]?.file && avatarUploadUrl) {
        const avatarFile = avatarFiles[0].file;

        // Validate file type
        if (!avatarFile.type.startsWith('image/')) {
          throw new Error('Avatar file must be an image');
        }

        // Validate file size (5MB limit)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (avatarFile.size > maxSize) {
          throw new Error('Avatar file size must be less than 5MB');
        }

        console.log('🔍 Starting avatar upload...');
        console.log('🔍 Avatar file details:', {
          fileName: avatarFile.name,
          fileSize: avatarFile.size,
          fileType: avatarFile.type,
        });

        const avatarUploadPromise = (async () => {
          console.log(`🔍 Uploading avatar:`, {
            fileName: avatarFile.name,
            fileSize: avatarFile.size,
            fileType: avatarFile.type,
          });

          try {
            await uploadImageFile(avatarFile, avatarUploadUrl);
            console.log(`✅ Avatar uploaded successfully`);
          } catch (error) {
            console.error(`❌ Exception uploading avatar:`, error);
            throw error;
          }
        })();
        allUploadPromises.push(avatarUploadPromise);
      } else {
        console.log('⚠️ SKIPPING avatar upload:', {
          hasAvatarFiles: avatarFiles.length > 0,
          hasValidFile: !!avatarFiles[0]?.file,
          hasUploadUrl: !!avatarUploadUrl,
          avatarFilesCount: avatarFiles.length,
        });
      }

      // Đợi tất cả uploads hoàn thành
      console.log('🔍 Total upload promises:', allUploadPromises.length);
      if (allUploadPromises.length > 0) {
        try {
          await Promise.all(allUploadPromises);
          console.log('🎉 All uploads completed successfully');

          // Hiển thị thông báo thành công cho upload
          showSuccess({
            title: t('admin:agent.upload.success', 'Upload thành công'),
            message: t(
              'admin:agent.upload.successMessage',
              'Tất cả file đã được upload thành công'
            ),
          });
        } catch (uploadError) {
          console.error('❌ Upload error:', uploadError);

          // Hiển thị thông báo lỗi cho upload
          showError({
            title: t('admin:agent.upload.error', 'Lỗi upload'),
            message:
              uploadError instanceof Error
                ? uploadError.message
                : t('admin:agent.upload.errorMessage', 'Có lỗi xảy ra khi upload file'),
          });

          throw new Error(
            `Upload failed: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`
          );
        }
      } else {
        console.log('ℹ️ No files to upload');
      }

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error submitting agent form:', error);

      // Hiển thị thông báo lỗi chung
      showError({
        title: t('admin:agent.form.error', 'Lỗi'),
        message:
          error instanceof Error
            ? error.message
            : t('admin:agent.form.errorMessage', 'Có lỗi xảy ra khi tạo agent'),
      });

      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Danh sách providers
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
    { type: TypeProviderEnum.GEMINI, name: 'Gemini' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
    { type: TypeProviderEnum.XAI, name: 'XAI' },
  ];

  return (
    <Card>
      <div className="flex justify-start items-center mb-6">
        <Typography variant="h4" className="font-semibold">
          {t('admin:agent.system.addAgent', 'Thêm Agent System')}
        </Typography>
      </div>

      <Form
        schema={createAgentSystemSchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.basicInfo', 'Thông tin cơ bản')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem name="name" label={t('admin:agent.form.name', 'Tên Agent')} required>
              <Input
                fullWidth
                placeholder={t('admin:agent.form.namePlaceholder', 'Nhập tên agent')}
              />
            </FormItem>

           
          </FormGrid>

          <FormItem
            name="instruction"
            label={t('admin:agent.form.instruction', 'Hướng dẫn')}
            required
          >
            <Textarea
              fullWidth
              rows={4}
              placeholder={t('admin:agent.form.instructionPlaceholder', 'Nhập hướng dẫn cho agent')}
            />
          </FormItem>

          <FormItem name="description" label={t('admin:agent.form.description', 'Mô tả')} required>
            <Textarea
              fullWidth
              rows={3}
              placeholder={t('admin:agent.form.descriptionPlaceholder', 'Nhập mô tả agent')}
            />
          </FormItem>
        </div>

        <Divider />

        {/* Avatar Upload */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.avatar', 'Avatar')}
          </Typography>

          <MultiFileUpload
            label={t('admin:agent.form.avatarUpload', 'Tải lên avatar')}
            accept="image/jpeg,image/png"
            placeholder={t('admin:agent.form.avatarHelp', 'Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)')}
            value={avatarFiles}
            onChange={handleAvatarChange}
            mediaOnly={true}
            showPreview={true}
            height="h-32"
          />
        </div>

        <Divider />

        {/* Model Configuration */}

        {/* Provider Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.provider', 'Loại Provider')}
          </Typography>

          <ResponsiveGrid
            maxColumns={{ xs: 2, sm: 3, md: 4, lg: 6, xl: 6 }}
            maxColumnsWithChatPanel={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 6 }}
            gap={{ xs: 4, md: 5, lg: 6 }}
          >
            {providers.map(provider => (
              <ProviderCard
                key={provider.type}
                provider={provider.type}
                name={provider.name}
                isSelected={selectedProvider === provider.type}
                onClick={handleProviderSelect}
                disabled={isSubmitting}
              />
            ))}
          </ResponsiveGrid>
        </div>

        <Divider />

        {/* Model and Vector Store Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.resources', 'Tài nguyên')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem name="modelId" label={t('admin:agent.form.model', 'Model')} required>
              <Select
                fullWidth
                loading={loadingSystemModels}
                placeholder={t('admin:agent.form.selectModel', 'Chọn model')}
                options={
                  systemModelsResponse?.items?.map(model => ({
                    value: model.id,
                    label: model.modelName,
                  })) || []
                }
                onChange={value => {
                  handleModelSelect(value as string);
                }}
              />
            </FormItem>

          
          </FormGrid>
        </div>

        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.modelConfig', 'Cấu hình Model')}
          </Typography>

          {selectedModel ? (
            <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
              {/* Temperature - hiển thị nếu model hỗ trợ */}
              {selectedModel.samplingParameters.includes('temperature') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.form.temperature', 'Temperature')}
                  </label>
                  <Slider
                    value={modelConfig['temperature'] || 1}
                    min={0}
                    max={2}
                    step={0.1}
                    onValueChange={value => handleModelConfigChange('temperature', value)}
                    valueSuffix=""
                  />
                </div>
              )}

              {/* Top P - hiển thị nếu model hỗ trợ */}
              {selectedModel.samplingParameters.includes('top_p') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.form.topP', 'Top P')}
                  </label>
                  <Slider
                    value={modelConfig['top_p'] || 1}
                    min={0}
                    max={1}
                    step={0.1}
                    onValueChange={value => handleModelConfigChange('top_p', value)}
                    valueSuffix=""
                  />
                </div>
              )}

              {/* Top K - hiển thị nếu model hỗ trợ */}
              {selectedModel.samplingParameters.includes('top_k') && (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-foreground">
                    {t('admin:agent.form.topK', 'Top K')}
                  </label>
                  <Slider
                    value={modelConfig['top_k'] || 1}
                    min={1}
                    max={100}
                    step={1}
                    onValueChange={value => handleModelConfigChange('top_k', value)}
                    valueSuffix=""
                  />
                </div>
              )}

              {/* Max Tokens */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-foreground">
                  {t('admin:agent.form.maxTokens', 'Max Tokens')}
                </label>
                <Slider
                  value={modelConfig['max_tokens'] || 1000}
                  min={100}
                  max={selectedModel?.maxTokens ? parseInt(selectedModel.maxTokens) : 8000}
                  step={100}
                  onValueChange={value => handleModelConfigChange('max_tokens', value)}
                  valueSuffix=""
                />
                {selectedModel?.maxTokens && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {t('admin:agent.template.maxTokensLimit', 'Model limit')}: {selectedModel.maxTokens}
                  </div>
                )}
              </div>
            </FormGrid>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Typography variant="body2">
                {t(
                  'admin:agent.supervisor.form.selectModelFirst',
                  'Vui lòng chọn model để cấu hình parameters'
                )}
              </Typography>
            </div>
          )}
        </div>

        <Divider />

        {/* MCP Systems Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.mcpSystems', 'MCP Systems')}
          </Typography>

          <AdminMCPConfig initialData={mcpConfigData} onSave={handleMCPConfigSave} mode="create" />
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-border">
          <IconCard
            icon="x"
            title={t('admin:agent.system.cancel')}
            onClick={onCancel}
            variant="secondary"
            disabled={isSubmitting}
          />
          <IconCard
            icon="check"
            title={t('admin:agent.system.form.create')}
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            isLoading={isSubmitting}
          />
        </div>
      </Form>
    </Card>
  );
};

export default AddAgentSystemForm;
